// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Items;
using Isto.Core.Pooling;
using UnityEngine;
using UPool;
using Zenject;

namespace Isto.Core.Automation
{
    /// <summary>
    /// <PERSON>les registering the core item with the automation system
    /// </summary>
	public class AutomationCoreItemRegister : MonoBeh<PERSON>our, IDataLoadCompleteHandler, IActionOnRecycle
    {
        [SerializeField] private ItemController _itemController = default;
        [Header("Hacking tools (don't touch)")]
        [Tooltip("Enable this before deleting if you want to delete items from the editor and have them unregister from automation")]
        [SerializeField] private bool _editorDeletionSupport = false;

        private AutomationSystem _autoSystem;
        private AutomationCoreItem.Factory _itemFactory;

        [Inject]
        public void Inject(AutomationSystem automationSystem, AutomationCoreItem.Factory itemFactory, PooledItemFactory pooledItemFactory)
        {
            _autoSystem = automationSystem;
            _itemFactory = itemFactory;
        }

        public void OnDataLoadComplete()
        {
            if (!gameObject.activeInHierarchy)
                return;

            if (GameState.LoadingFromSave)
                return;

            AutomationCoreItem automationItem = _itemFactory.Create(new CoreItemParams(_itemController.itemPile.item.itemID, _itemController.itemPile.count, transform.position));
            _autoSystem.TryAddCoreItemToSpace(transform.position, automationItem, false, true);

            // CoreItems are all culled
            // So we know we got to destroy the visuals after registering (display system will generate new ones)
            // Only question is whether it's a pooled item or not
            PoolableObject poolHandle = this.GetComponent<PoolableObject>();
            PooledInteractableItem interactable = this.GetComponent<PooledInteractableItem>();
            if (poolHandle != null && interactable != null)
            {
                // If it's a pooled item from the scene, it cannot be returned to the pool, but if we destroy it it will
                // complain for being destroyed by someone other than the pool. So we first have to tell it it's not actually pooled
                interactable.DeconfigurePooling();
            }

#if AUTOMATION_SETUP_LOGGING
            Debug.Log($"AutomationCoreItemRegister.OnDataLoadComplete destroys {gameObject.name}.");
#endif

            if (interactable != null)
            {
                interactable.DestroyGameObject();
            }
            else
            {
                // I assume this happens when you load a game with Deer Traps in it
                // Right now the deer traps load as core items (dropped inventory) instead of activatable trap items, so I can't test this.
                Debug.LogWarning($"Core Item {this.gameObject.name} does not have a PooledInteractableItem so it will be destroyed directly.");
                Destroy(this.gameObject);
            }
        }

        // Using Enable instead of Start so the setup is performed each time an instanced is taken from the pool and enabled
        protected void OnEnable()
        {
            // If item hasn't been set yet, register for event, otherwise check if setup in automation and if not, register
            if (!_itemController.HasItemBeenSet)
                _itemController.ItemSet += OnItemSet;
            else if (!_itemController.InitializedInAutomation)
                SetupInAutomation();
        }

        private void OnItemSet(object sender, System.EventArgs e)
        {
            // Note: In the case of autocollected items, at this point they are "initialized in automation" but don't have an automation space
            if (!_itemController.InitializedInAutomation)
            {
                SetupInAutomation();
            }

            _itemController.ItemSet -= OnItemSet;
        }

        public void SetupInAutomation()
        {
            AutomationCoreItem item = null;

            // Sometimes the item has already been set but it's not registered on a space yet, in that case keep the existing item
            if (_itemController.HasItemBeenSet)
            {
                item = _itemController.AutomationItem;
            }
            else
            {
                item = _itemFactory.Create(new CoreItemParams(_itemController.itemPile.item.itemID, _itemController.itemPile.count, transform.position));
            }

            _autoSystem.TryAddCoreItemToSpace(transform.position, item, autoCollectItem: false, triggerEvent: false);

            _itemController.SetItem(item, _autoSystem);
        }

        private void OnDestroy()
        {
            if (_editorDeletionSupport)
            {
                _itemController.DoPickupAction();
            }
        }

        public void OnRecycle()
        {
            if (!_itemController.HasItemBeenSet)
                _itemController.ItemSet -= OnItemSet;
        }
    }
}