// Copyright Isto Inc.
using Isto.Core.Beings;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Items;
using UnityEngine;
using UPool;
using Zenject;

namespace Isto.Core.Automation
{
    /// <summary>
    /// Used on Advanced Items that don't function in the automation system but still need to reserve a space in it to block items, etc.
    /// <PERSON>les registering in the AutomationSystem and removing itself when it is picked up
    /// </summary>
    public class AutomationGenericProcessorRegister : MonoBehaviour, IActivatable, IAutomationGridSpaceDisplay, IActionOnPickup, IDataLoadCompleteHandler
    {
        [SerializeField] private AdvancedItem _item = default;
        [SerializeField] private bool _visualCulling = true;
        [SerializeField] private bool _pooledObject = true;

        private AutomationGenericProcessor _systemProcessor;
        private AutomationGridSpace _gridSpace;

        // Injected

        private AutomationSystem _autoSystem;
        private AutomationGenericProcessor.Factory _genericFactory;
        private AutomationSystemDisplay _autoSystemDisplay;

        [Inject]
        public void Inject(AutomationSystem automationSystem, AutomationGenericProcessor.Factory processorFactory, AutomationSystemDisplay systemDisplay)
        {
            _autoSystem = automationSystem;
            _genericFactory = processorFactory;
            _autoSystemDisplay = systemDisplay;
        }

        public void OnDataLoadComplete()
        {
            SetupInAutomationSystem(addToArea: false);

            if (_pooledObject)
            {
                PoolableObject poolHandle = gameObject.GetComponent<PoolableObject>();
                PooledInteractableItem interactable = gameObject.GetComponent<PooledInteractableItem>();
                if (poolHandle != null && interactable != null)
                {
                    if (!GameState.LoadingFromSave)
                    {
                        // If it's a pooled item from the scene, it cannot be returned to the pool, but if we destroy it it will
                        // complain for being destroyed by someone other than the pool. So we first have to tell it it's not actually pooled
                        interactable.DeconfigurePooling();
                    }
                }
                else
                {
                    Debug.LogError($"AutomationGenericProcessorRegister.OnDataLoadComplete: item {this.gameObject.name} should be pooled but it is not, is this a legitimate use case?", this.gameObject);
                }

                // After setup in automation, destroy this gameobject as the display system will take over displaying this when it's on screen
                if (_visualCulling)
                {
                    if (interactable != null)
                    {
                        interactable.DestroyGameObject();
                    }
                    else
                    {
                        Debug.LogWarning($"AutomationGenericProcessorRegister Destroying the non-pooled item {this.gameObject.name} directly.");
                        Destroy(gameObject);
                    }
                }
            }

            // After setup in automation, destroy this gameobject as the display system will take over displaying this when it's on screen
            if (_visualCulling)
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// When placement is complete
        /// </summary>
        public void Activate()
        {
            SetupInAutomationSystem(addToArea: true);
        }

        private void SetupInAutomationSystem(bool addToArea = true)
        {
            // Only register active items
            if (!gameObject.activeInHierarchy)
                return;

            _systemProcessor = _genericFactory.Create(_item.itemID);
            _systemProcessor.SetVisible(_visualCulling);

            if (_autoSystem.TryAddItemProcessor(transform.position, _systemProcessor, forward: Vector3.back, triggerEvent: false))
            {
                _gridSpace = _autoSystem.GetOrCreateGridSpace(transform.position);

                if (addToArea && _visualCulling)
                {
                    _autoSystemDisplay.AddObjectToAreaDictionary(gameObject);
                }

                IAutomationGridSpaceDisplay[] displayUsers = GetComponents<IAutomationGridSpaceDisplay>();

                for (int i = 0; i < displayUsers.Length; i++)
                {
                    displayUsers[i].SetGridSpace(_gridSpace);
                }

                Health health = GetComponent<Health>();

                if (health != null)
                {
                    health.Killed += OnKilled;
                }
            }
            else
            {
                Debug.LogWarning($"Couldn't add {_item.itemID} generic processor to automation grid space at {transform.position}.");
            }
        }

        public void SetGridSpace(AutomationGridSpace space)
        {
            _gridSpace = space;
            _systemProcessor = space.itemProcessor as AutomationGenericProcessor;
        }

        public void DoPickupAction()
        {
            if (_autoSystem == null || _autoSystemDisplay == null || _gridSpace == null)
            {
                Debug.LogError($"Null Automation Variables in DoPickUpAction on item:{_item.itemID}. Force destroying item");
                Destroy(gameObject, 0.1f);
                return;
            }

            _autoSystem.TryRemoveProcessor(_gridSpace.position);

            if (_visualCulling)
                _autoSystemDisplay.RemoveObjectFromAreaDictionary(gameObject);
        }

        // Event Handler

        private void OnKilled(object sender, HealthEventArgs e)
        {
            DoPickupAction();
        }
    }
}