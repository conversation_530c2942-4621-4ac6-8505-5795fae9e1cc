// Copyright Isto Inc.

namespace Isto.Core.Automation
{
    public interface IAutomationGridSpaceDisplay
    {
        // The display system will create the display item and assign it a pre-existing grid space in three cases:
        // - When loading items from a save file
        // - When loading existing items on a map
        // - When items get un-culled
        // This leaves creation through Item Placement which won't call this because the flow is "reversed" in that case.
        // (i.e. we place the display item which in turn creates itself an automation item on its space)
        void SetGridSpace(AutomationGridSpace space);
    }
}