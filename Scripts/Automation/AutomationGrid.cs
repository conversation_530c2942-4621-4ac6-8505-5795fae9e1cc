// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Automation
{
    [Serializable]
    public class AutomationGrid
    {
        // This arbitrary size is tied to our addressing system in HashCoordinate
        // Currently the grid supports from (-1023,-1023) to (1023,1023) and the array is 32MB on a 64b system
        public const int AUTOMATION_TABLE_SIZE = 4194304;
        public const int INVALID_ARRAY_INDEX = -1;

        /// <summary>
        /// Dictionary only containes spaces that have grid spaces that have automation pieces on them.  If space is empty it is removed from Dictionary to only interate over
        /// spaces that have something on them
        /// </summary>
        public Dictionary<int, AutomationGridSpace>.ValueCollection ActiveSpaces { get { return _automationGrid.Values; } }
        public Dictionary<int, AutomationGridSpace>.ValueCollection SleepingSpaces { get { return _sleepingGridSpaces.Values; } }

        public IEnumerable<AutomationGridSpace> AllSpaces { get { return _automationGrid.Values.Union(_sleepingGridSpaces.Values); } }

        private Dictionary<int, AutomationGridSpace> _automationGrid;
        private Dictionary<int, AutomationGridSpace> _sleepingGridSpaces;

        private AutomationGridSpace[] _automationTable;

        // I'm keeping a dummy parameterless constructor around as a trap to make sure any temp AutomationGrid instances don't
        // have to carry the initialization and if we ever decide to inject this class I think this will be called.
        // If you really want to create an AutomationGrid, just use the other constructor.
        public AutomationGrid() { }

        // Need a parameter so this constructor is not used accidentally to do some default instantiations,
        // as we initialize a 32MB array in here. (I think some code tries to invoke the empty constructor
        // which sends unity in a frenzy of memory allocation if I don't do this)
        public AutomationGrid(bool dummyFlag)
        {
            _automationGrid = new Dictionary<int, AutomationGridSpace>();
            _sleepingGridSpaces = new Dictionary<int, AutomationGridSpace>();
            _automationTable = new AutomationGridSpace[AUTOMATION_TABLE_SIZE];
        }

        public bool TryAddToGrid(AutomationGridSpace space)
        {
            if (DoesSpaceExist((int)space.position.x, (int)space.position.z))
                return false;
            else
            {
                int index = HashCoordinate((int)space.position.x, (int)space.position.z);
                _automationGrid.Add(index, space);
                _automationTable[index] = space;
                return true;
            }
        }

        public bool DoesSpaceExist(float x, float y)
        {
            return DoesSpaceExist(Mathf.RoundToInt(x), Mathf.RoundToInt(y));
        }

        public bool DoesSpaceExist(int x, int y)
        {
            int index = HashCoordinate(x, y);

            return _automationTable[index] != null;
        }

        // Warning: this method is significantly less efficient if we're accessing spaces thousands of times per frame. Work with integers instead.
        public bool TryGetSpace(float x, float y, ref AutomationGridSpace space)
        {
            return TryGetSpace(Mathf.RoundToInt(x), Mathf.RoundToInt(y), ref space);
        }

        public bool TryGetSpace(int x, int y, ref AutomationGridSpace space)
        {
            int index = HashCoordinate(x, y);

            // Retrieving a specific item from the dictionary is not efficient so for now as long as we know the index we'll access it via the array
            space = _automationTable[index];

            return space != null;
        }

        public void RemoveSpace(AutomationGridSpace space)
        {
            int index = HashCoordinate((int)space.position.x, (int)space.position.z);

            if (!_automationGrid.Remove(index))
                _sleepingGridSpaces.Remove(index);

            _automationTable[index] = null;
        }

        public void ClearGrid()
        {
            _automationGrid = new Dictionary<int, AutomationGridSpace>();
            _sleepingGridSpaces = new Dictionary<int, AutomationGridSpace>();
            _automationTable = new AutomationGridSpace[AUTOMATION_TABLE_SIZE];
        }

        public void PutSpaceToSleep(AutomationGridSpace space)
        {
            int spaceCoord = HashCoordinate(Mathf.RoundToInt(space.position.x), Mathf.RoundToInt(space.position.z));

            if (_automationGrid.ContainsKey(spaceCoord) && !_sleepingGridSpaces.ContainsKey(spaceCoord))
            {
                _automationGrid.Remove(spaceCoord);
                _sleepingGridSpaces.Add(spaceCoord, space);

                space.SetSleepingFlag();
            }
            else if (_sleepingGridSpaces.ContainsKey(spaceCoord))
            {
                Debug.LogError($"Automation Grid Space is already sleeping at {space.position}");
            }
            else if (!_automationGrid.ContainsKey(spaceCoord))
            {
                Debug.LogError($"Trying to put to sleep a grid space that is not in the active spaces dictionary. {space.position}");
            }
        }

        public void WakeUpSleepingSpace(AutomationGridSpace space)
        {
            int spaceCoord = HashCoordinate(Mathf.RoundToInt(space.position.x), Mathf.RoundToInt(space.position.z));

            if (_sleepingGridSpaces.ContainsKey(spaceCoord) && !_automationGrid.ContainsKey(spaceCoord))
            {
                _sleepingGridSpaces.Remove(spaceCoord);
                _automationGrid.Add(spaceCoord, space);

                space.ClearSleepingFlag();
            }
            else if (!_sleepingGridSpaces.ContainsKey(spaceCoord))
            {
                Debug.LogError($"Automation Grid Space is not sleeping at {space.position}");
            }
            else if (_automationGrid.ContainsKey(spaceCoord))
            {
                Debug.LogError($"Trying to wake up a grid space that is not in the sleeping spaces dictionary. {space.position}");
            }
        }

        /// <summary>
        /// This method uses a pairing function to convert a coordinate pair to unique single integer value.
        /// There are fancy ways to do this but they involve a lot of arithmetic, and this method will get used a lot.
        /// Using knowledge of the domain we can tweak a very simple and very specialized approach instead.
        /// </summary>
        /// <param name="x">X position, in the range of [-1023,1023]</param>
        /// <param name="y">Y position, in the range of [-1023,1023]</param>
        /// <returns>A unique index in the range of [0,4194303] (22 bits of addressing) </returns>
        public static int HashCoordinate(int x, int y) // Make the parameters into shorts?
        {
            // The following code assumes a real life max of 2047 (11 bits), halfsize of 1023, and hash max of 4 194 303.
            // This will be enough for the moment because the largest map we have fits approximately between (-500,-500) and (500,500)

            // These are the limits we arbitrarily defined.
            if (x < -1023 || x > 1023 || y < -1023 || y > 1023)
                return INVALID_ARRAY_INDEX;

            uint ux = (uint)x + 1023; // will always be positive
            uint uy = (uint)y + 1023;

            uint uResult = (ux << 11) + uy;

            // arbitrary size of our grid space array and hard limit to indexing.
            if (uResult >= AUTOMATION_TABLE_SIZE)
                return INVALID_ARRAY_INDEX; // this will cause exceptions if unhandled as we try to read the array at -1

            // I'd rather return a uint for clarity, but we're setup for all the dictionaries to rely on int keys so that's a good bit
            return (int)uResult;
        }

        // For testing only - for production code see AutomationQuad and QuadTreeNode instead
        public void SearchForSpaces(Vector3Int topLeft, Vector3Int botRight, Func<AutomationGridSpace, bool> predicate, ref List<Vector3> spacePositions)
        {
            spacePositions.Clear();

            SearchInternal(topLeft.x, topLeft.z, botRight.x, botRight.z, predicate, ref spacePositions);
        }

        // For testing only - for production code see AutomationQuad and QuadTreeNode instead
        private void SearchInternal(int leftX, int leftZ, int rightX, int rightZ, Func<AutomationGridSpace, bool> predicate, ref List<Vector3> spacePositions)
        {
            for (int i = leftX; i < rightX; i++)
            {
                for (int j = leftZ; j < rightZ; j++)
                {
                    AutomationGridSpace space = null;
                    if (TryGetSpace(i, j, ref space))
                    {
                        if (space != null && predicate(space))
                        {
                            spacePositions.Add(space.position);
                        }
                    }
                }
            }
        }

        public override string ToString()
        {
            string output = "";

            output += $"Total Automation Spaces: {_automationGrid.Values.Count} \n";

            foreach (AutomationGridSpace space in _automationGrid.Values)
            {
                output += $"({space.position.x},{space.position.z}).  HashPos:{HashCoordinate((int)space.position.x, (int)space.position.z)}." +
                    $"  Resource:{space.Resource?.ID}.  Processor:{space.itemProcessor?.ToString()}.  CoreItem:{space.ActiveItem?.ID}\n";
            }

            return output;
        }
    }
}