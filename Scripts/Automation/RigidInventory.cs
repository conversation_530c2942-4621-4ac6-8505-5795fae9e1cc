// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Automation
{
    /// <summary>
    /// Special Inventory implementation that has set items in each slot.  Item piles need their expected item to be set before you can start adding items
    /// </summary>
    public class RigidInventory : Inventory, IRigidInventory
    {
        public RigidInventory(int pileCount, int pileSize) : base(pileCount, pileSize)
        {
        }

        public bool HasItemsSetOnPiles()
        {
            if (Items.Count == 0)
                return false;

            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i].item == null)
                    return false;
            }

            return true;
        }

        public void SetItemConstraintForPile(int index, CoreItem item)
        {
            if (Items.Count < 0 || index >= Items.Count)
            {
                Debug.LogError("Trying to add at index that is out of bounds.  Index: " + index);
                return;
            }

            Items[index].item = item;
            Items[index].count = 0;
        }

        public override int Add(CoreItem item, int count = 1, bool sendUpdateEvent = true)
        {
            //Make sure the item list is initialized
            if (Items == null)
                Items = new List<ItemPile>(PileCount);

            // If the item is null for some reason, don't deposit anything
            if (item == null)
                return 0;

            List<ItemPile> existingPiles = Items.FindAll(x => x.item == item);

            int remainingToAdd = count;

            //If there are already any piles of that item in the container, just add the pile amount if we have space.
            for (int i = 0; i < existingPiles.Count; i++)
            {
                int remainingSpace = PileSize - existingPiles[i].count;

                //If there is only room for some items, fill up the pile then try again on the next inventory pile
                if (remainingSpace < remainingToAdd)
                {
                    existingPiles[i].count += remainingSpace;
                    remainingToAdd -= remainingSpace;
                }
                //Deposite all the remaining items to add on the pile and return count as all the items were deposited.
                else
                {
                    existingPiles[i].count += remainingToAdd;

                    remainingToAdd = 0;
                }
            }

            int amountAdded = count - remainingToAdd;

            // If any items were added, send events
            if (amountAdded != 0 && sendUpdateEvent)
            {
                SendUpdateEvents(item, amountAdded);
            }

            return amountAdded;
        }

        public override int AddAt(ItemPile pile, int index)
        {
            if (Items.Count <= index)
                return 0;

            int deposited = 0;

            // If its empty at the desired index or the same item, try adding the items
            if (Items[index].item == pile.item)
            {
                Items[index].item = pile.item;

                int spaceAvail = PileSize - Items[index].count;

                deposited = spaceAvail >= pile.count ? pile.count : spaceAvail;

                Items[index].count += deposited;
            }

            // If something was deposited, send message to any components on this object that are listening
            if (deposited != 0)
                SendUpdateEvents(pile.item, deposited);

            return deposited;
        }

        public override int Remove(CoreItem item, int count = 1, bool sendUpdateEvent = true)
        {
            if (Items == null || Items.Count == 0 || item == null)
                return 0;

            return Remove(item.itemID, count);
        }

        public int Remove(string itemID, int count = 1)
        {
            if (Items == null || Items.Count == 0 || string.IsNullOrEmpty(itemID))
                return 0;

            var piles = Items.FindAll(x => x.item.itemID.Equals(itemID, StringComparison.OrdinalIgnoreCase)).OrderBy(x => x.count);

            int remainingToRemove = count;

            using (var pilesEnumerator = piles.GetEnumerator())
            {
                while (pilesEnumerator.MoveNext())
                {
                    ItemPile next = pilesEnumerator.Current;

                    //If pile is large enough, take all items from this pile
                    if (next.count > remainingToRemove)
                    {
                        next.count -= remainingToRemove;
                        remainingToRemove = 0;
                        break;
                    }
                    //If not enough on pile, take all off pile and continue looping
                    else
                    {
                        remainingToRemove -= next.count;
                        next.count = 0;
                    }
                }
            }

            int amountRemoved = count - remainingToRemove;

            if (amountRemoved != 0)
                SendUpdateEvents(null, -amountRemoved);

            return amountRemoved;
        }

        public override ItemPile RemoveAt(int index, int count)
        {
            if (Items.Count < index + 1)
                return null;

            ItemPile pileAtIndex = Items[index];
            ItemPile outputPile = new ItemPile();

            //If not enough items at index, remove all of them and return the pile
            if (pileAtIndex.count <= count)
            {
                outputPile.Set(pileAtIndex);
                Items[index].count = 0;
            }
            //If enough items at index, subtract from this inventory and return the requested amount
            else
            {
                pileAtIndex.count -= count;
                outputPile.Set(pileAtIndex.item, count);
            }

            if (outputPile.count != 0)
                SendUpdateEvents(outputPile.item, outputPile.count);

            return outputPile;
        }

        public override List<ItemPile> RemoveAll(bool triggerEvent = true)
        {
            List<ItemPile> allItems = new List<ItemPile>();

            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i].item != null && Items[i].count != 0)
                    allItems.Add(new ItemPile(Items[i].item, Items[i].count));

                Items[i].count = 0;
            }

            // If there were items, clear list and send message
            if (allItems.Count != 0 && triggerEvent)
                SendUpdateEvents(null, 0);              // Just passing in empty values for removing all items from inventory, should probably update

            return allItems;
        }

        public override int GetSpaceAvailable(CoreItem item)
        {
            if (item != null)
                return GetSpaceAvailable(item.itemID);

            return 0;
        }

        public override int GetSpaceAvailable(string itemID)
        {
            //Make sure the item list is initialized
            if (Items == null)
                Items = new List<ItemPile>(PileCount);

            int spaceAvailable = 0;

            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i].item != null && Items[i].item.itemID.Equals(itemID, StringComparison.OrdinalIgnoreCase))
                {
                    spaceAvailable += PileSize - Items[i].count;
                }
            }

            return spaceAvailable;
        }
    }
}