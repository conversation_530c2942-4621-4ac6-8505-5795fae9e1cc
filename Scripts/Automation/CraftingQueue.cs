// Copyright Isto Inc.
using FMODUnity;
using Isto.Core.Audio;
using Isto.Core.Beings;
using Isto.Core.Items;
using Isto.Core.UI;
using System;
using System.Collections.Generic;
using UnityEngine;
using Zenject;

namespace Isto.Core.Automation
{
    [RequireComponent(typeof(ItemContainer))]
    public class CraftingQueue : MonoBehaviour
    {
        // Public Variables

        public int maxQueueLength = 10;

        [EventRef]
        public string itemCraftedSoundRef;

        public float CurrentProgress { get { return _currentRecipe == null ? 0 : _cookTimer / _currentRecipe.cookTime; } }
        public float CurrentCookTime { get { return _currentRecipe == null ? 0 : _currentRecipe.cookTime; } }
        public float CookSpeedMultiplier { get; set; } = 1.0f;

        // Events

        public event EventHandler<ItemEventArgs> ItemCrafted;

        // Private Variables

        private IUIMessages _uiMessages;
        private IGameSounds _sounds;

        private List<Recipe> _queuedRecipes;
        private ItemContainer _inventory;

        private Recipe _currentRecipe;
        private float _cookTimer;

        // Lifecycle Events

        [Inject]
        public void Inject([Inject(Id = UIMessageHandlerType.ProgressMessage, Optional = true)] IUIMessages uIMessages, IGameSounds gameSounds)
        {
            _uiMessages = uIMessages;
            _sounds = gameSounds;
        }

        void Awake()
        {
            _queuedRecipes = new List<Recipe>();
            _inventory = GetComponent<ItemContainer>();
        }

        void Update()
        {
            if (_currentRecipe == null && _queuedRecipes.Count > 0)
            {
                StartNextRecipe();
            }

            if (_currentRecipe != null)
            {
                Cook();
            }
        }

        /// <summary>
        /// Sets the next recipe to be crafted from the Queue.  The input items have already been removed from the inventory
        /// </summary>
        private void StartNextRecipe()
        {
            _currentRecipe = _queuedRecipes[0];
            _queuedRecipes.RemoveAt(0);

            _cookTimer = 0f;

            CoreItem outputItem = _currentRecipe.outputs[0].item;
        }

        // Methods		

        /// <summary>
        /// Adds recipe to the current queue for crafting
        /// </summary>
        /// <param name="recipe">Recipe to add to the queue</param>
        /// <returns>True if recipe added successfully, false otherwise.  False indicates too many items in queue already</returns>
        public bool EnqueueRecipe(Recipe recipe)
        {
            if (_queuedRecipes.Count >= maxQueueLength)
                return false;

            _queuedRecipes.Add(recipe);

            if (_uiMessages != null)
            {
                // Add item to crafting queue UI Messages
                _uiMessages.CreateProgressMessage(recipe.outputs[0].item.itemName, this, recipe.outputs[0].item.icon);
            }
            
            return true;
        }

        public bool HasRecipeInQueue(Recipe recipe)
        {
            if (_queuedRecipes.Contains(recipe) || _currentRecipe == recipe)
                return true;
            else
                return false;
        }

        private void Cook()
        {
            _cookTimer += Time.deltaTime * CookSpeedMultiplier;

            if (_cookTimer > _currentRecipe.cookTime)
            {
                // Only complete cooking if there is room for recipe outputs
                if (_inventory.HasSpaceFor(_currentRecipe.outputs))
                {
                    for (int i = 0; i < _currentRecipe.outputs.Count; i++)
                    {
                        ItemPile outputItem = _currentRecipe.outputs[i];

                        // Only add to inventory if not upgrade item
                        if (outputItem.item.GetType() != typeof(UpgradeItem))
                            _inventory.Add(outputItem);
                    }

                    _sounds.PlayOneShot(itemCraftedSoundRef, transform.position);

                    ItemCrafted?.Invoke(this, new ItemEventArgs(_inventory, _currentRecipe.outputs[0].item));

                    _currentRecipe = null;
                    _cookTimer = 0f;
                }
            }
        }

        // Data Methods

        public List<string> GetAllRecipesInQueue()
        {
            List<string> recipes = new List<string>();

            if (_currentRecipe != null)
                recipes.Add(_currentRecipe.outputs[0].item.itemID);

            foreach (Recipe recipe in _queuedRecipes)
            {
                recipes.Add(recipe.outputs[0].item.itemID);
            }

            return recipes;
        }

        /// <summary>
        /// Adds the recipe to the crafting queue without subtracting items from the players inventory.
        /// </summary>
        /// <param name="recipe"></param>
        public void ForceRecipeIntoCraftingQueue(Recipe recipe)
        {
            _queuedRecipes.Add(recipe);
            
            if (_uiMessages != null)
            {
                // Add item to crafting queue UI Messages
                _uiMessages.CreateProgressMessage(recipe.outputs[0].item.itemName, this, recipe.outputs[0].item.icon);
            }
        }

        public bool TryCancelQueueItem(int index)
        {
            Recipe recipeToCancel;

            if (index == 0)
            {
                if (_currentRecipe == null)
                    return false;

                recipeToCancel = _currentRecipe;
            }
            else
            {
                // Subtracting one as the current cooking item isn't in the list so index 0 is the 2nd display slot
                int adjustedIndex = index - 1;

                if (adjustedIndex >= 0 && adjustedIndex < _queuedRecipes.Count)
                {
                    recipeToCancel = _queuedRecipes[adjustedIndex];
                }
                else
                {
                    Debug.LogWarning("Index out of range for trying to cancel item in Crafting Queue. Index: " + adjustedIndex);
                    return false;
                }
            }

            if (_inventory.HasSpaceFor(recipeToCancel.inputs))
            {
                for (int i = 0; i < recipeToCancel.inputs.Count; i++)
                {
                    _inventory.Add(recipeToCancel.inputs[i]);
                }

                if (index == 0)
                {
                    _currentRecipe = null;
                    _cookTimer = 0f;
                }
                else
                {
                    _queuedRecipes.RemoveAt(index - 1);
                }

                return true;
            }
            else if (_inventory is PlayerInventory playerInv)
            {
                _sounds.PlayOneShot(playerInv.fullInventorySoundRef, playerInv.transform.position);
            }

            return false;
        }
    }
}