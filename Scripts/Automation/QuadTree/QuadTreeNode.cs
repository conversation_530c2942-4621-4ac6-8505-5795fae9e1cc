// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Automation
{
    public class QuadTreeNode
    {
        public Vector3Int position;
        public AutomationGridSpace space;

        public QuadTreeNode(Vector3Int position, AutomationGridSpace space)
        {
            this.position = position;
            this.space = space;
        }
    }

    public class AutomationQuad
    {
        public QuadTreeNode node;

        public AutomationQuad topLeftTree;
        public AutomationQuad topRightTree;
        public AutomationQuad botLeftTree;
        public AutomationQuad botRightTree;

        public int topLeftX;
        public int topLeftZ;
        public int botRightX;
        public int botRightZ;

        public AutomationQuad(Vector3Int topLeft, Vector3Int bottomRight)
        {
            topLeftX = topLeft.x;
            topLeftZ = topLeft.z;
            botRightX = bottomRight.x;
            botRightZ = bottomRight.z;

            this.topLeftTree = null;
            this.topRightTree = null;
            this.botLeftTree = null;
            this.botRightTree = null;
        }

        public void Insert(QuadTreeNode node, int maxDepth = 12)
        {
            if (node == null)
                return;

            if (maxDepth <= 0)
            {
                Debug.LogError("Reached max depth in Quad Tree");
                return;
            }

            if (!IsInboundry(node.position))
                return;

            // We are at a quad of unit area
            // We cannot subdivide this quad further
            if (Mathf.Abs(botRightX - topLeftX) <= 1 && Mathf.Abs(topLeftZ - botRightZ) <= 1)
            {
                if (this.node == null)
                    this.node = node;

                return;
            }

            // Right Side?
            if ((botRightX + topLeftX) / 2 <= node.position.x)
            {
                // Indicates topRightTree
                if ((topLeftZ + botRightZ) / 2 <= node.position.z)
                {
                    if (topRightTree == null)
                        topRightTree = new AutomationQuad(
                            new Vector3Int((botRightX + topLeftX) / 2, 0, topLeftZ),
                            new Vector3Int(botRightX, 0, (topLeftZ + botRightZ) / 2));

                    topRightTree.Insert(node, maxDepth - 1);
                }
                // Indicates botRightTree
                else
                {
                    if (botRightTree == null)
                        botRightTree = new AutomationQuad(
                            new Vector3Int((botRightX + topLeftX) / 2, 0, (topLeftZ + botRightZ) / 2),
                            new Vector3Int(botRightX, 0, botRightZ));

                    botRightTree.Insert(node, maxDepth - 1);
                }
            }
            // Left Side
            else
            {
                // Indicates topLeftTree
                if ((topLeftZ + botRightZ) / 2 <= node.position.z)
                {
                    if (topLeftTree == null)
                        topLeftTree = new AutomationQuad(
                            new Vector3Int(topLeftX, 0, topLeftZ),
                            new Vector3Int((botRightX + topLeftX) / 2, 0, (topLeftZ + botRightZ) / 2));

                    topLeftTree.Insert(node, maxDepth - 1);
                }
                // Indicates botLeftTree
                else
                {
                    if (botLeftTree == null)
                        botLeftTree = new AutomationQuad(
                            new Vector3Int(topLeftX, 0, (topLeftZ + botRightZ) / 2),
                            new Vector3Int((botRightX + topLeftX) / 2, 0, botRightZ));

                    botLeftTree.Insert(node, maxDepth - 1);
                }
            }
        }

        public void Remove(Vector3Int p, int maxDepth = 12)
        {
            if (maxDepth <= 0)
            {
                Debug.LogError("Reached max depth in Quad Tree");
                return;
            }

            if (!IsInboundry(p))
                return;

            // We are at a quad of unit area
            // We cannot subdivide this quad further
            if (Mathf.Abs(botRightX - topLeftX) <= 1 && Mathf.Abs(topLeftZ - botRightZ) <= 1)
            {
                if (this.node != null && this.node.position == p)
                    this.node = null;

                return;
            }

            // Right Side?
            if ((botRightX + topLeftX) / 2 <= p.x)
            {
                // Indicates topRightTree
                if ((topLeftZ + botRightZ) / 2 <= p.z)
                {
                    if (topRightTree != null)
                    {
                        topRightTree.Remove(p, maxDepth - 1);

                        if (topRightTree.IsEmpty())
                            topRightTree = null;
                    }
                }
                // Indicates botRightTree
                else
                {
                    if (botRightTree != null)
                    {
                        botRightTree.Remove(p, maxDepth - 1);

                        if (botRightTree.IsEmpty())
                            botRightTree = null;
                    }
                }
            }
            // Left Side
            else
            {
                // Indicates topLeftTree
                if ((topLeftZ + botRightZ) / 2 <= p.z)
                {
                    if (topLeftTree != null)
                    {
                        topLeftTree.Remove(p, maxDepth - 1);

                        if (topLeftTree.IsEmpty())
                            topLeftTree = null;
                    }
                }
                // Indicates botLeftTree
                else
                {
                    if (botLeftTree != null)
                    {
                        botLeftTree.Remove(p, maxDepth - 1);

                        if (botLeftTree.IsEmpty())
                            botLeftTree = null;
                    }
                }
            }
        }

        //public QuadTreeNode Search(Vector3Int p)
        //{
        //    if (!IsInboundry(p)) return null;

        //    // We are at a quad of unit length
        //    // We cannot subdivide this quad further
        //    if (node != null) 
        //        return node;

        //    if ((topLeftX + botRightX) / 2 >= p.x)
        //    {
        //        // Indicates topLeftTree
        //        if ((topLeftZ + botRightZ) / 2 >= p.z)
        //        {
        //            if (topLeftTree == null)
        //                return null;

        //            return topLeftTree.Search(p);
        //        }
        //        // Indicates botLeftTree
        //        else
        //        {
        //            if (botLeftTree == null)
        //                return null;

        //            return botLeftTree.Search(p);
        //        }
        //    }
        //    else
        //    {
        //        // Indicates topRightTree
        //        if ((topLeftZ + botRightZ) / 2 >= p.z)
        //        {
        //            if (topRightTree == null)
        //                return null;

        //            return topRightTree.Search(p);
        //        }

        //        // Indicates botRightTree
        //        else
        //        {
        //            if (botRightTree == null)
        //                return null;

        //            return botRightTree.Search(p);
        //        }
        //    }
        //}

        public List<QuadTreeNode> Search(Vector3Int topLeft, Vector3Int botRight)
        {
            List<QuadTreeNode> results = new List<QuadTreeNode>();

            SearchInternal(topLeft.x, topLeft.z, botRight.x, botRight.z, ref results);

            return results;
        }

        protected void SearchInternal(int topLeftX, int topLeftZ, int botRightX, int botRightZ, ref List<QuadTreeNode> results)
        {
            if (!IsOverlapping(topLeftX, topLeftZ, botRightX, botRightZ))
                return;

            if (node != null)
            {
                results.Add(node);
                return;
            }

            if (topLeftTree != null)
                topLeftTree.SearchInternal(topLeftX, topLeftZ, botRightX, botRightZ, ref results);
            if (topRightTree != null)
                topRightTree.SearchInternal(topLeftX, topLeftZ, botRightX, botRightZ, ref results);
            if (botLeftTree != null)
                botLeftTree.SearchInternal(topLeftX, topLeftZ, botRightX, botRightZ, ref results);
            if (botRightTree != null)
                botRightTree.SearchInternal(topLeftX, topLeftZ, botRightX, botRightZ, ref results);
        }

        public void SearchForSpaces(Vector3Int topLeft, Vector3Int botRight, Func<AutomationGridSpace, bool> predicate, ref List<Vector3> spacePositions)
        {
            spacePositions.Clear();

            SearchInternal(topLeft.x, topLeft.z, botRight.x, botRight.z, predicate, ref spacePositions);
        }

        protected void SearchInternal(int leftX, int leftZ, int rightX, int rightZ, Func<AutomationGridSpace, bool> predicate, ref List<Vector3> spacePositions)
        {
            if (!IsOverlapping(leftX, leftZ, rightX, rightZ))
                return;

            if (node != null && node.space != null && predicate(node.space))
            {
                spacePositions.Add(node.space.position);
                return;
            }

            if (topLeftTree != null)
                topLeftTree.SearchInternal(leftX, leftZ, rightX, rightZ, predicate, ref spacePositions);
            if (topRightTree != null)
                topRightTree.SearchInternal(leftX, leftZ, rightX, rightZ, predicate, ref spacePositions);
            if (botLeftTree != null)
                botLeftTree.SearchInternal(leftX, leftZ, rightX, rightZ, predicate, ref spacePositions);
            if (botRightTree != null)
                botRightTree.SearchInternal(leftX, leftZ, rightX, rightZ, predicate, ref spacePositions);
        }

        private bool IsInboundry(Vector3Int p)
        {
            return (p.x >= topLeftX && p.x <= botRightX && p.z <= topLeftZ && p.z >= botRightZ);
        }

        private bool IsOverlapping(int leftX, int leftZ, int rightX, int rightZ)
        {
            if (leftX == rightX || leftZ == rightZ || botRightX == this.topLeftX || this.topLeftZ == botRightZ)
            {
                return false;
            }

            // If one rectangle is on left side of other
            if (leftX > this.botRightX || this.topLeftX > rightX)
            {
                return false;
            }

            // If one rectangle is above other
            if (rightZ > this.topLeftZ || this.botRightZ > leftZ)
            {
                return false;
            }

            return true;
        }

        private bool IsEmpty()
        {
            return node == null && topLeftTree == null && topRightTree == null && botLeftTree == null && botRightTree == null;
        }
    }
}
