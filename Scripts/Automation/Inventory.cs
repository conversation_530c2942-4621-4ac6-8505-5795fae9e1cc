// Copyright Isto Inc.
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Isto.Core.Automation
{
    public class Inventory : IInventory
    {
        // Events

        public event EventHandler<ItemEventArgs> InventoryChanged;

        // Public Variables

        public int PileCount { get; protected set; }
        public int PileSize { get; set; }

        public List<string> validItemIDs = new List<string>();
        public List<ItemPile> Items { get; protected set; }

        // Methods		

        public Inventory(int pileCount, int pileSize)
        {
            PileCount = pileCount;
            PileSize = pileSize;

            InitializeList();
        }

        public void InitializeList()
        {
            if (Items != null)
                return;

            Items = new List<ItemPile>(PileCount);

            // Fill the list with empty item piles
            for (int i = 0; i < PileCount; i++)
            {
                Items.Add(new ItemPile());
            }
        }

        /// <summary>
        /// Attempts to deposit the item into this container.  
        /// </summary>
        /// <param name="item">CoreItem to deposit.</param>
        /// <param name="count">Number of the CoreItem to deposit. Default is one.</param>
        /// <returns>Number of CoreItems that were successfully deposited.</returns>
        public virtual int Add(CoreItem item, int count = 1, bool sendUpdateEvent = true)
        {
            //Make sure the item list is initialized
            if (Items == null)
                Items = new List<ItemPile>(PileCount);

            if (validItemIDs.Count != 0 && !validItemIDs.Contains(item.itemID))
                return 0;

            // If the item is null for some reason, don't deposit anything
            if (item == null)
                return 0;

            List<ItemPile> existingPiles = Items.FindAll(x => x.item == item);

            int remainingToAdd = count;

            //If there are already any piles of that item in the container, just add the pile amount if we have space.
            for (int i = 0; i < existingPiles.Count; i++)
            {
                int remainingSpace = PileSize - existingPiles[i].count;

                //If there is only room for some items, fill up the pile then try again on the next inventory pile
                if (remainingSpace < remainingToAdd)
                {
                    existingPiles[i].count += remainingSpace;
                    remainingToAdd -= remainingSpace;
                }
                //Deposite all the remaining items to add on the pile and return count as all the items were deposited.
                else
                {
                    existingPiles[i].count += remainingToAdd;

                    remainingToAdd = 0;
                }
            }

            while (remainingToAdd > 0)
            {
                // Find first open index
                int openIndex = Items.FindIndex(x => x.item == null || x.count == 0);

                // If no open spaces found, break from while loop
                if (openIndex == -1)
                {
                    break;
                }

                if (remainingToAdd > PileSize)
                {
                    Items[openIndex].Set(item, PileSize);
                    remainingToAdd -= PileSize;
                }
                else
                {
                    Items[openIndex].Set(item, remainingToAdd);
                    remainingToAdd = 0;
                }
            }

            int amountAdded = count - remainingToAdd;

            // If any items were added, send events
            if (amountAdded != 0 && sendUpdateEvent)
            {
                SendUpdateEvents(item, amountAdded);
            }

            return amountAdded;
        }

        /// <summary>
        /// Attempts to deposit the item pile into the container.  Internally just calls the Add method with
        /// CoreItem and count parameters
        /// </summary>
        /// <param name="pile">Item pile representing the item and amount to deposit</param>
        /// <returns>Number of CoreItems that were successfully deposited.</returns>
        public int Add(ItemPile pile)
        {
            return Add(pile.item, pile.count);
        }

        /// <summary>
        /// Attemps to add the item pile at the desired index position in the container.
        /// </summary>
        /// <param name="pile"></param>
        /// <param name="index"></param>
        /// <returns>Returns the amount of the pile that was successfully deposited</returns>
        public virtual int AddAt(ItemPile pile, int index)
        {
            if (Items.Count <= index)
                return 0;

            int deposited = 0;

            // If its empty at the desired index or the same item, try adding the items
            if (Items[index].item == null || Items[index].item == pile.item)
            {
                Items[index].item = pile.item;

                int spaceAvail = PileSize - Items[index].count;

                deposited = spaceAvail >= pile.count ? pile.count : spaceAvail;

                Items[index].count += deposited;
            }

            // If something was deposited, send message to any components on this object that are listening
            if (deposited != 0)
                SendUpdateEvents(pile.item, deposited);

            return deposited;
        }

        /// <summary>
        /// Attempts to remove the count of CoreItem from this container.
        /// </summary>
        /// <param name="item">CoreItem to remove</param>
        /// <param name="count">Amount of the CoreItem to remove.</param>
        /// <returns>The amount that was successfully removed.</returns>
        public virtual int Remove(CoreItem item, int count = 1, bool sendUpdateEvent = true)
        {
            if (Items == null || Items.Count == 0 || item == null)
                return 0;

            List<ItemPile> piles = Items.FindAll(x => x.item == item).OrderBy(x => x.count).ToList();

            int remainingToRemove = count;

            for (int i = 0; i < piles.Count; i++)
            {
                //If pile is large enough, take all items from this pile
                if (piles[i].count > remainingToRemove)
                {
                    piles[i].count -= remainingToRemove;
                    remainingToRemove = 0;
                    break;
                }
                //If not enough on pile, take all off pile and continue looping
                else
                {
                    remainingToRemove -= piles[i].count;
                    piles[i].count = 0;
                }

                // Clear slot if none left
                if (piles[i].count == 0)
                    piles[i].item = null;
            }

            int amountRemoved = count - remainingToRemove;

            if (amountRemoved != 0 && sendUpdateEvent)
                SendUpdateEvents(item, -amountRemoved);

            return amountRemoved;
        }

        /// <summary>
        /// Attempts to remove an amount of items from a specific position in the container
        /// </summary>
        /// <param name="index">The position in the container to remove the items from.</param>
        /// <param name="count">Amount of items at that position to remove.</param>
        /// <returns>ItemPile containing which item was at that position and the requested amount if enough, otherwise as many as were available.</returns>
        public virtual ItemPile RemoveAt(int index, int count)
        {
            if (Items.Count < index + 1)
                return null;

            ItemPile pileAtIndex = Items[index];
            ItemPile outputPile = new ItemPile();

            //If not enough items at index, remove all of them and return the pile
            if (pileAtIndex.count <= count)
            {
                outputPile.Set(pileAtIndex);
                Items[index].Clear();
            }
            //If enough items at index, subtract from this inventory and return the requested amount
            else
            {
                pileAtIndex.count -= count;
                outputPile.Set(pileAtIndex.item, count);
            }

            if (outputPile.count != 0)
            {
                SendUpdateEvents(outputPile.item, -outputPile.count);
            }

            return outputPile;
        }

        public virtual ItemPile RemoveHalfAt(int index)
        {
            if (Items.Count < index + 1)
                return null;

            int amountToRemove = Mathf.CeilToInt((float)Items[index].count / 2.0f);

            return RemoveAt(index, amountToRemove);
        }

        /// <summary>
        /// Removes the entire pile at index location specified
        /// </summary>
        /// <param name="index">Index position to remove the ItemPile from</param>
        /// <returns></returns>
        public virtual ItemPile RemoveAt(int index)
        {
            if (Items.Count < index + 1)
                return null;

            return RemoveAt(index, Items[index].count);
        }

        /// <summary>
        /// Takes all items out of the container
        /// </summary>
        /// <param name="triggerEvent">Should the remove action trigger the Inventory Update event to be triggered</param>
        /// <returns>List containing all the item piles from the container.</returns>
        public virtual List<ItemPile> RemoveAll(bool triggerEvent = true)
        {
            List<ItemPile> allItems = new List<ItemPile>();

            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i].item != null && Items[i].count != 0)
                    allItems.Add(new ItemPile(Items[i].item, Items[i].count));

                Items[i].Clear();
            }

            // If there were items, clear list and send message
            if (allItems.Count != 0 && triggerEvent)
                SendUpdateEvents(null, 0);              // Just passing in empty values for removing all items from inventory, should probably update

            return allItems;
        }

        public List<ItemPile> RemoveAllBut(List<Item> invalidItems, bool triggerEvent = true)
        {
            List<ItemPile> allItems = new List<ItemPile>();

            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i].item != null && Items[i].count != 0)
                {
                    if (!invalidItems.Contains(Items[i].item))
                    {
                        allItems.Add(new ItemPile(Items[i].item, Items[i].count));

                        Items[i].Clear();
                    }

                }

            }

            // If there were items, clear list and send message
            if (allItems.Count != 0 && triggerEvent)
                SendUpdateEvents(null, 0); // Just passing in empty values for removing all items from inventory, should probably update

            return allItems;
        }

        /// <summary>
        /// Gets the total count of the passed in item in the container.
        /// </summary>
        /// <param name="item">Item to get the count for.</param>
        /// <returns>Amount of item that exists in the container.</returns>
        public int GetCountOfItem(CoreItem item)
        {
            if (Items == null || item == null)
                return 0;

            int amt = 0;

            for (int i = 0; i < Items.Count; i++)
            {
                amt += Items[i].item == item ? Items[i].count : 0;
            }

            return amt;
        }

        public int GetCountOfItem(string itemID)
        {
            if (Items == null || string.IsNullOrEmpty(itemID))
                return 0;

            int amt = 0;

            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i].item != null)
                    amt += Items[i].item.itemID.Equals(itemID, StringComparison.OrdinalIgnoreCase) ? Items[i].count : 0;
            }

            return amt;
        }

        public int GetCountAt(int index)
        {
            if (Items == null)
                return 0;
            if (Items.Count < index)
                return 0;

            return Items[index].count;
        }

        public int GetHalfCountAt(int index)
        {
            if (Items == null)
                return 0;
            if (Items.Count < index + 1)
                return 0;

            int half = Mathf.CeilToInt((float)Items[index].count / 2.0f);
            return half;
        }

        /// <summary>
        /// Returns the number of item piles currently in the inventory, NOT the count of unique items in the container.
        /// </summary>
        /// <returns>Number of ItemPile's in container</returns>
        public int GetNumberOfPiles()
        {
            int total = 0;

            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i] != null && Items[i].item != null)
                {
                    total++;
                }
            }

            return total;
        }

        /// <summary>
        /// Gets the total number of all items in the container.
        /// </summary>
        /// <returns></returns>
        public int GetTotalNumberOfItems()
        {
            int total = 0;

            for (int i = 0; i < Items.Count; i++)
            {
                if (Items[i] != null && Items[i].item != null)
                {
                    total += Items[i].count;
                }
            }

            return total;
        }

        /// <summary>
        /// Returns the item at the index specified.
        /// </summary>
        /// <param name="index">Index of item pile to retrieve.</param>
        /// <returns>ItemPile of item at specified index if found, null otherwise.</returns>
        public ItemPile GetItemAt(int index)
        {

            if (index >= Items.Count)
                return null;

            return Items[index];
        }

        /// <summary>
        /// Gets the index in the inventory for the first pile containing the item.
        /// </summary>
        /// <param name="item">Item to find the index of</param>
        /// <returns>The first index for the item in the container, -1 if item not found.</returns>
        public int GetFirstIndexOfItem(CoreItem item)
        {
            int index = Items.FindIndex(x => x.item == item);

            return index;
        }

        /// <summary>
        /// Checks if this container will accept the item passed in.
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public bool AcceptsItem(CoreItem item)
        {
            if (validItemIDs.Count != 0 && !validItemIDs.Contains(item.itemID))
                return false;
            else
                return true;
        }

        public bool AcceptsItem(ItemPile pile)
        {
            if (pile.item != null)
                return AcceptsItem(pile.item);

            return false;
        }

        public void SortAlphabetically()
        {
            List<ItemPile> allItems = RemoveAll(false);

            for (int i = 0; i < allItems.Count; i++)
            {
                Add(allItems[i].item, allItems[i].count, false);
            }

            ItemPileComparer compare = new ItemPileComparer();
            Items.Sort((x, y) => compare.Compare(x, y));
        }

        /// <summary>
        /// Returns how many of the item can be placed into the container.
        /// </summary>
        /// <param name="item">CoreItem to be placed in container.</param>
        /// <returns></returns>
        public virtual int GetSpaceAvailable(CoreItem item)
        {
            if (item != null)
                return GetSpaceAvailable(item.itemID);
            else
                return -1;
        }

        public virtual int GetSpaceAvailable(string itemID)
        {
            //Make sure the item list is initialized
            if (Items == null)
                Items = new List<ItemPile>(PileCount);

            if (validItemIDs.Count != 0 && !validItemIDs.Contains(itemID))
                return 0;

            int spaceAvailable = 0;

            for (int i = 0; i < Items.Count; i++)
            {
                ItemPile next = Items[i];

                if (next.item != null && next.item.itemID.Equals(itemID, StringComparison.OrdinalIgnoreCase))
                {
                    spaceAvailable += PileSize - next.count;
                }
                else if (next.item == null)
                {
                    spaceAvailable += PileSize;
                }
            }

            return spaceAvailable;
        }

        public int GetNumOpenSlots()
        {
            // Find how many open spaces we have
            return Items.FindAll(x => x.item == null || x.count == 0).Count;
        }

        /// <summary>
        /// Calculate how many the percentage of Open
        /// </summary>
        /// <returns>Percent from 0-1 of how full the scrap chest is</returns>
        public float GetContainerPercentUsed()
        {
            int totalSpace = PileCount * PileSize;
            int usedSpace = GetTotalNumberOfItems();

            return (float)usedSpace / (float)totalSpace;
        }

        /// <summary>
        /// Checks if the container has enough space for all the items in the list.  Used by recipes to check if there is space before crafting
        /// </summary>
        /// <param name="outputs">List of ItemPiles to check if there is space for</param>
        /// <returns>True if there is space, false otherwise</returns>
        public bool HasSpaceFor(List<ItemPile> outputs)
        {
            if (outputs.Count > 1)
                Debug.LogError("Inventory space check not fully robust enough to check for multiple outputs, please extend functionality");

            bool hasSpace = true;

            for (int i = 0; i < outputs.Count; i++)
            {
                hasSpace &= GetSpaceAvailable(outputs[i].item) >= outputs[i].count;
            }

            return hasSpace;
        }

        /// <summary>
        /// Checks the ingredient list for the passed in item and returns if the player has enough of the ingredients to craft one
        /// of it.
        /// </summary>
        /// <param name="item">CoreItem to be checked</param>
        /// <returns>True if the player inventory has enough of the required items, false otherwise.</returns>
        public bool HasIngredients(CoreItem item)
        {
            bool hasAll = true;

            if (item == null || item.GetRecipe() == null)
                return false;

            for (int i = 0; i < item.GetRecipe().inputs.Count; i++)
            {
                ItemPile ingredient = item.GetRecipe().inputs[i];

                hasAll = hasAll && GetCountOfItem(ingredient.item) >= ingredient.count;
            }

            return hasAll;
        }

        /// <summary>
        /// Checks if the inventory has all the required inputs for the recipe
        /// </summary>
        /// <param name="recipe"></param>
        /// <returns></returns>
        public bool HasAllInputs(Recipe recipe)
        {
            bool allInputs = true;

            for (int i = 0; i < recipe.inputs.Count; i++)
            {
                allInputs &= GetCountOfItem(recipe.inputs[i].item) >= recipe.inputs[i].count;
            }

            return allInputs;
        }

        public void SetPileCount(int count)
        {
            if (PileCount != count)
            {
                if (Items.Count > count)
                {
                    int startIndex = count - 1;
                    int amtToRemove = Items.Count - count;

                    if (startIndex >= 0)
                        Items.RemoveRange(startIndex, amtToRemove);
                    else
                        Items.Clear();
                }
                else
                {
                    int amoutToAdd = count - Items.Count;

                    for (int i = 0; i < amoutToAdd; i++)
                    {
                        Items.Add(new ItemPile());
                    }
                }

                PileCount = count;
            }
        }

        // Events

        protected void SendUpdateEvents(CoreItem item, int change)
        {
            InventoryChanged?.Invoke(this, new ItemEventArgs(this, item, change));
        }
    }
}