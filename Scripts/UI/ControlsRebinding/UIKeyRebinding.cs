// Copyright Isto Inc.

using Isto.Core.Enums;
using Isto.Core.Inputs;
using Rewired;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.Serialization;

namespace Isto.Core.UI
{
    /// <summary>
    /// Note that despite the name, this class handles both keyboard keys and controller buttons
    /// It is meant to represent any simple action ("button press" type events).
    /// </summary>
    public class UIKeyRebinding : UIControlRebindingBase
    {
        // UNITY HOOKUP

        [FormerlySerializedAs("_action")]
        [Header("Action To Rebind")]
        [EnumDropdown(typeof(UserActions))]
        [SerializeField] private string _userAction;


        // PROPERTIES

        public override string InputActionName => _userAction;
        public override bool IsAxis => false;


        // OTHER METHODS

        public string SplitString(string stringToChange)
        {
            Regex r = new Regex(@"
                (?<=[A-Z])(?=[A-Z][a-z]) |
                 (?<=[^A-Z])(?=[A-Z]) |
                 (?<=[A-Za-z])(?=[^A-Za-z])", RegexOptions.IgnorePatternWhitespace);

            return r.<PERSON>lace(stringToChange, " ");
        }

        public override void RefreshDisplay()
        {
            _actionId = ReInput.mapping.GetActionId(_userAction);
            _map = GetControllerMapForCategory(_category);
            _aem = GetFirstElementMapFromControllerMap(_map, _userAction);


            if (_aem != null)
            {
                SetupControlsWithElementMap();
            }
            else // If null no mapping currently exists
            {
                SetupControlsWithNoMapping();
            }

            if (!_isInitialized)
            {
                _cachedSprite = _keyImage.sprite;
                _isInitialized = true;
            }
            else
            {
                Button.SetDirtyFlag(_cachedSprite != _keyImage.sprite);
            }
        }

        private void SetupControlsWithElementMap()
        {
            Sprite actionSprite = GetGlyphForAction(_aem);
            if (actionSprite != null)
            {
                _keyImage.sprite = actionSprite;
                _keyImage.enabled = true;
                _controlKeyTextbox.enabled = false;
            }
            else
            {
                _keyImage.enabled = false;
                _controlKeyTextbox.text = _aem.keyCode != KeyCode.None ? _aem.keyCode.ToString() : _aem.elementIdentifierName;
                _controlKeyTextbox.enabled = true;
            }
        }

        private void SetupControlsWithNoMapping()
        {
            _keyImage.sprite = _glyphOverride != null ? _glyphOverride : null;
            _controlKeyTextbox.text = "";
            _controlKeyTextbox.enabled = true;
        }
    }
}