// Copyright Isto Inc.
using Isto.Core.StateMachine;
using TMPro;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// This popup has no button and it will auto-close after a certain event or time limit
    /// </summary>
    public class UISimpleTimedModalState : UISimpleModalStateBase
    {
        [SerializeField] private TextMeshProUGUI _mainTextbox;
        [SerializeField] private TextMeshProUGUI _countdownTextbox;

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

            CancelAllowed = false;
        }

        public override void Exit(MonoStateMachine controller)
        {
            base.Exit(controller);

            //_uiSounds.PlayButtonClickSound();
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            base.ReturnFromSubState(controller, previousState);
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            // No cancel or submit logic here
            // We're instead waiting for any input from the player for consideration
            // For the keyboard, Esc is hardcoded as a cancel, for controller, there is no such thing
            // There is a countdown timer to abort the polling, but it is managed by Rewired
            // The coroutine to wait for the remapping probably should run in this? unless passing the torch to the conflict popup is then too hard to do
            return base.Run(controller);
        }

        public void SetMainText(string text)
        {
            _mainTextbox.text = text;
        }

        public void SetCountdownText(string text)
        {
            _countdownTextbox.text = text;
        }
    }
}