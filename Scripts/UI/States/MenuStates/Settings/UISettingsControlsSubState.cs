// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Inputs;
using Isto.Core.Localization;
using Isto.Core.StateMachine;
using Rewired;
using System;
using UnityEngine;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    /// <summary>
    /// This class manages the UI settings related to control rebinding sub categories for both keyboard and controller devices.
    /// It also handles the display and functionality of various UI elements such as rebinding prompts, conflict prompts,
    /// and device-specific control layouts.
    /// </summary>
    public class UISettingsControlsSubState : UISettingsBaseSubState
    {
        public enum TargetInputDevice { Keyboard, Controller }


        // UNITY HOOKUP

        [Header("Pop up States")]
        [SerializeField] private UISimpleTimedModalState _rebindingUIPrompt;
        [SerializeField] private UISimpleAcknowledgementModalState _rebindingImpossiblePrompt;
        [SerializeField] private UISimpleConfirmModalState _conflictUIPrompt;
        [SerializeField] private UISimpleConfirmModalState _confirmationUIPrompt;

        [Header("Rebinding Localization")] // Press desired button
        [SerializeField] private LocalizedString _actionNameStartString;

        [Header("Key Conflict Localization")] // Confirm deleting old binding before overriding it
        [SerializeField] private LocalizedString _conflictMessageLoc;
        [SerializeField] private LocalizedString _timeRemainingLoc;

        [Header("Cannot Bind Localization")] // Can't remap controller without a controller
        [SerializeField] private LocalizedString _noControllerFoundLoc;
        [SerializeField] private LocalizedString _inputForbiddenLoc;
        [SerializeField] private LocalizedString _reservedKeyLoc;

        [Header("Viewport Content")]
        [SerializeField] private RectTransform _viewportContentContainer;

        [Header("Keyboard Setup")]
        [SerializeField] private RectTransform _keyboardControlsContainer;
        [SerializeField] private CoreButtonHighlightableController _keyboardHighlightableButton;

        [Header("Controller Setup")]
        [SerializeField] private RectTransform _controllerControlsContainer;
        [SerializeField] private CoreButtonHighlightableController _controllerHighlightableButton;

        [Header("Controller Category SubStates")]
        [SerializeField] private UISettingsGameInputSubState _keyboardMouseSubState;
        [SerializeField] private UISettingsGameInputSubState _controllerSubState;


        // OTHER FIELDS

        private UISettingsGameInputSubState _currentGameInputSubState;
        private UISettingsGameInputSubState _nextGameInputSubState;


        // PROPERTIES

        public override bool HasChanges => _keyboardMouseSubState.HasChanges || _controllerSubState.HasChanges;

        public override Selectable FirstSelectable => _currentGameInputSubState?.SubTabButton;

        public override Selectable DefaultSelectable => _currentGameInputSubState?.DefaultSelectable;

        public override SettingsSubMenusEnum SettingsSubMenu => SettingsSubMenusEnum.Controls;

        public TargetInputDevice CurrentDevice => GetCurrentGameInputCategory();

        private bool IsNavigationInSubTabButtonBar => _keyboardHighlightableButton.IsSelected || _controllerHighlightableButton.IsSelected;


        // LIFECYCLE

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

            SetupPushdownStateMachine(controller);
            EnterInitialDeviceSubState();
            RefreshSubTabMenuNavigation();
        }

        public override void Exit(MonoStateMachine controller)
        {
            base.Exit(controller);

            _keyboardHighlightableButton.SetHighlighted(false);
            _controllerHighlightableButton.SetHighlighted(false);
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            base.ReturnFromSubState(controller, previousState);

            // The current flow has either KeyboardAndMouseSubState or ControllerSubState always on top of this state
            // within the stack. When exiting either one of those states, we also want to exit this current state.
            _menuController.ExitSubState();
        }


        // EVENT HANDLING

        // Since we have InputSubStates that will be pushed on top of the stack we do not need to register the base events
        // as it is handled in the other substates.
        protected override void RegisterEvents()
        {
            _controllerSubState.OnTabDirection += InputSubState_OnTabDirection;
#if !PLATFORM_GAMECORE
            _keyboardMouseSubState.OnTabDirection += InputSubState_OnTabDirection;
#endif
        }

        private void InputSubState_OnTabDirection(TabDirection tabDirection)
        {
            InvokeTabDirection(tabDirection);
        }

        protected override void UnregisterEvents()
        {
            _controllerSubState.OnTabDirection -= InputSubState_OnTabDirection;
#if !PLATFORM_GAMECORE
            _keyboardMouseSubState.OnTabDirection -= InputSubState_OnTabDirection;
#endif
        }

        // This is used by the Select Keyboard button that is located in the Unity Editor. It can be found in the
        // controllerKBM_Buttons container within the controls substate container.
        // We shouldn't hit this when in Xbox, but we have the pre-processor check in place just in case
        public void Button_SelectKeyboardLayout()
        {
#if !PLATFORM_GAMECORE
            RequestChangeCurrentSubState(_keyboardMouseSubState);
#endif
        }

        // This is used by the Select Controller button that is located in the Unity Editor. It can be found in the
        // controllerKBM_Buttons container within the controls substate container.
        public void Button_SelectControllerLayout()
        {
            RequestChangeCurrentSubState(_controllerSubState);
        }


        // ACCESSORS

        public UIControlRebindingBase GetControlRebinder(string actionName, Pole pole)
        {
            UIControlRebindingBase result = null;

            switch (CurrentDevice)
            {
                case TargetInputDevice.Keyboard:
                    UIControlRebindingBase[] rebindingButtons = _keyboardMouseSubState.KeyRebindings;

                    for (int i = 0; i < rebindingButtons.Length; i++)
                    {
                        if (rebindingButtons[i].InputActionName.Equals(actionName) &&
                            rebindingButtons[i].AxisDirection == pole)
                        {
                            result = rebindingButtons[i];
                            break;
                        }

                    }
                    break;
                case TargetInputDevice.Controller:
                    UIControlRebindingBase[] joyRebindingButtons = _controllerSubState.KeyRebindings;

                    for (int i = 0; i < joyRebindingButtons.Length; i++)
                    {
                        if (joyRebindingButtons[i].InputActionName.Equals(actionName) &&
                            joyRebindingButtons[i].AxisDirection == pole)
                        {
                            result = joyRebindingButtons[i];
                            break;
                        }
                    }
                    break;
            }

            return result;
        }

        public void SetRebindingTimeRemaining(float time)
        {
            string text = time.ToString("F0") + _timeRemainingLoc;
            _rebindingUIPrompt.SetCountdownText(text);
        }

        private string GetLocActionNameForAxis(string actionName, Pole pole)
        {
            UIControlRebindingBase control = GetControlRebinder(actionName, pole);
            string result = control != null ? control.ActionName : actionName;
            return result;
        }

        private void SetCurrentSubState(UISettingsGameInputSubState currentSubState)
        {
            _currentGameInputSubState = currentSubState;
            _currentGameInputSubState.KeepNavInStateButtonsBar = this.KeepNavInStateButtonsBar;
            this.KeepNavInStateButtonsBar = false;
            _currentGameInputSubState.KeepNavInSubTabButtonsBar = IsNavigationInSubTabButtonBar;

            UpdateCurrentDeviceVisuals();
            RefreshSubTabMenuNavigation();
        }

        private TargetInputDevice GetCurrentGameInputCategory()
        {
            TargetInputDevice targetInputDevice = TargetInputDevice.Keyboard;

            if (_currentGameInputSubState == _keyboardMouseSubState)
            {
                targetInputDevice = TargetInputDevice.Keyboard;
            }
            else if (_currentGameInputSubState == _controllerSubState)
            {
                targetInputDevice = TargetInputDevice.Controller;
            }

            return targetInputDevice;
        }

        public override void ResetToDefaults() { }


        // OTHER METHODS

        private void RefreshSubTabMenuNavigation()
        {
            // Down from tab is subtab. this one should have been set already? or soon - it's being overwritten ATM.
            // At least updating it should mean it's always going to the open one.
            UIUtils.SetNavigation(_currentGameInputSubState.TabButton,
                                  UIUtils.NavigationDirection.Down, _currentGameInputSubState.SubTabButton);

            // Navigation down from the subtab needs to land in the page that's open.
            UIUtils.SetNavigation(_keyboardHighlightableButton.GetSelectable(),
                                  UIUtils.NavigationDirection.Down, _currentGameInputSubState.DefaultSelectable);
            UIUtils.SetNavigation(_controllerHighlightableButton.GetSelectable(),
                                  UIUtils.NavigationDirection.Down, _currentGameInputSubState.DefaultSelectable);

            // Up from page is the subtab bar. These won't change AFAIK so they could be done in a different flow.
            UIUtils.SetNavigation(_keyboardMouseSubState.DefaultSelectable,
                                  UIUtils.NavigationDirection.Up, _keyboardHighlightableButton.GetSelectable());
            UIUtils.SetNavigation(_controllerSubState.DefaultSelectable,
                                  UIUtils.NavigationDirection.Up, _controllerHighlightableButton.GetSelectable());

            // Up from subtab bar is the tab bar. Also not changing.
            UIUtils.SetNavigation(_keyboardHighlightableButton.GetSelectable(),
                                  UIUtils.NavigationDirection.Up, _keyboardMouseSubState.TabButton);
            UIUtils.SetNavigation(_controllerHighlightableButton.GetSelectable(),
                                  UIUtils.NavigationDirection.Up, _controllerSubState.TabButton);
        }

        public void RefreshControlUIState()
        {
            _currentGameInputSubState.RefreshControlsDisplay();
            _currentGameInputSubState.VerifyRemapDirtyFlags();
        }

        public void ShowRebindingPopUp(string actionName)
        {
            if(!CheckIfStackIsReady())
                return;

            string textToDisplay = _actionNameStartString.ToString();
            textToDisplay = textToDisplay.Replace("{action}", $"<color=#{ColorUtility.ToHtmlStringRGBA(Constants.ATRIO_PINK_LIGHT)}>{actionName}</color>");
            _rebindingUIPrompt.SetMainText(textToDisplay);
            _menuController.EnterSubState(_rebindingUIPrompt);
        }

        public void HideRebindingPopUp()
        {
            _rebindingUIPrompt.Hide();
            RefreshControlUIState();
        }

        public void ShowConflictPopUp(ElementAssignmentConflictInfo conflictInfo, Action<bool> onPopUpClose)
        {
            if(!CheckIfStackIsReady())
                return;

            string inputKeyName;
            switch (conflictInfo.controllerType)
            {
                case ControllerType.Mouse:
                case ControllerType.Joystick:
                    inputKeyName = conflictInfo.elementMap.elementIdentifierName;
                    break;
                case ControllerType.Keyboard:
                case ControllerType.Custom:
                default:
                    inputKeyName = conflictInfo.keyCode.ToString();
                    break;
            }

            string conflictedActionName = conflictInfo.action.name; //ex MoveVertical
            string actionNameLoc;
            actionNameLoc = GetLocActionNameForAxis(conflictedActionName, conflictInfo.elementMap.axisContribution);

            string conflictMessage = $"<color=#{ColorUtility.ToHtmlStringRGBA(Constants.ATRIO_PINK_LIGHT)}>{inputKeyName}</color>" +
                                     $" {_conflictMessageLoc} <color=#{ColorUtility.ToHtmlStringRGBA(Constants.ATRIO_PINK_LIGHT)}>{actionNameLoc}</color>";

            _conflictUIPrompt.SetMainText(conflictMessage);
            _conflictUIPrompt.SetCallbacks(() => { onPopUpClose.Invoke(true); }, () => { onPopUpClose.Invoke(false); });
            _menuController.EnterSubState(_conflictUIPrompt); // note that this delays selection of default control by one frame
        }

        public void ShowControllerRequiredPopup(Action onClose = null)
        {
            if(!CheckIfStackIsReady())
                return;

            _rebindingImpossiblePrompt.SetDetailsText(Loc.Get(_noControllerFoundLoc));
            _rebindingImpossiblePrompt.SetCallbacks(onClose, null);
            _menuController.EnterSubState(_rebindingImpossiblePrompt);
        }

        public void ShowBindingForbiddenPopup(Action onClose = null)
        {
            if(!CheckIfStackIsReady())
                return;

            _rebindingImpossiblePrompt.SetDetailsText(Loc.Get(_inputForbiddenLoc));
            _rebindingImpossiblePrompt.SetCallbacks(onClose, null);
            _menuController.EnterSubState(_rebindingImpossiblePrompt);
        }

        public void ShowOverrideFailedPopup(Action onClose = null)
        {
            if(!CheckIfStackIsReady())
                return;

            _rebindingImpossiblePrompt.SetDetailsText(_reservedKeyLoc);
            _rebindingImpossiblePrompt.SetCallbacks(onClose, null);
            _menuController.EnterSubState(_rebindingImpossiblePrompt);
        }

        private void GoToInitialSubState(UISettingsGameInputSubState nextSubState)
        {
            SetCurrentSubState(nextSubState);
            _menuController.EnterSubState(nextSubState);
        }

        private void SetupPushdownStateMachine(MonoStateMachine controller)
        {
            if (controller is MonoPushdownStateMachine stateMachine)
            {
                _menuController = stateMachine;
            }
            else
            {
                Debug.LogError("Using Settings menu without a push down menu controller, this won't work");
            }
        }

        private void EnterInitialDeviceSubState()
        {
            UISettingsGameInputSubState nextSubState = Controls.UsingController ? _controllerSubState : _keyboardMouseSubState;

#if PLATFORM_GAMECORE
            // Always go to the controller substate when playing on Xbox
            nextSubState = _controllerSubState;
#endif

            GoToInitialSubState(nextSubState);
        }

        private void UpdateCurrentDeviceVisuals()
        {
            if (CurrentDevice == TargetInputDevice.Keyboard)
            {
                _keyboardHighlightableButton.SetHighlighted(true);
                _controllerHighlightableButton.SetHighlighted(false);
                _viewportContentContainer.sizeDelta = new Vector2(0, _keyboardControlsContainer.sizeDelta.y);
            }
            else
            {
                _keyboardHighlightableButton.SetHighlighted(false);
                _controllerHighlightableButton.SetHighlighted(true);
                _viewportContentContainer.sizeDelta = new Vector2(0, _controllerControlsContainer.sizeDelta.y);
            }
        }

        private bool CheckIfStackIsReady()
        {
            bool isStackReady = true;

            if (_menuController.GetCurrentState() is UISimpleModalStateBase)
            {
                Debug.LogWarning("Requesting to enter a substate for a popup, but there is already a popup on the state stack. This will probably lead to some menu stack issues.");
                isStackReady = false;
            }

            return isStackReady;
        }

        private void RequestChangeCurrentSubState(UISettingsGameInputSubState nextState)
        {
            _nextGameInputSubState = nextState;

            if (_currentGameInputSubState.HasChanges)
            {
                ShowGameInputSubStateChangesConfirmationPopup();
            }
            else
            {
                ChangeToNextState();
            }
        }

        private void ShowGameInputSubStateChangesConfirmationPopup()
        {
            _confirmationSubState.SetCallbacks(ConfirmationSubState_ApplyGameInputChanges, ConfirmationSubState_DiscardGameInputChanges); // callbacks happen after popup substate pops itself
            _menuController.EnterSubState(_confirmationSubState);
            _confirmationSubState.SetMainText(Loc.GetString(Constants.UI_SETTINGS_MESSAGE_SAVE_SETTINGS));
        }

        private void ChangeToNextState()
        {
            SetCurrentSubState(_nextGameInputSubState);
            _menuController.ChangeState(_currentGameInputSubState);
            _nextGameInputSubState = null;
            InvokeSubCategoryChanged();
        }

        private void ConfirmationSubState_ApplyGameInputChanges()
        {
            _currentGameInputSubState.ApplySettingsChanges();
            ChangeToNextState();
        }

        private void ConfirmationSubState_DiscardGameInputChanges()
        {
            _currentGameInputSubState.DiscardSettingsChanges();
            ChangeToNextState();
        }
    }
}