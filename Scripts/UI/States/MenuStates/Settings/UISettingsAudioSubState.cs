// Copyright Isto Inc.

using Isto.Core.Audio;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Sub-state class for managing the games audio settings.
    /// </summary
    public class UISettingsAudioSubState : UISettingsBaseSubState
    {
        // UNITY HOOKUP

        [Header("Audio Settings")]
        [SerializeField] private UISettingsOptionSlider _masterVolumeSlider;
        [SerializeField] private UISettingsOptionSlider _musicVolumeSlider;
        [SerializeField] private UISettingsOptionSlider _sfxVolumeSlider;
        [SerializeField] private UISettingsOptionSlider _dialogueVolumeSlider;
        [SerializeField] private UISettingsOptionSlider _ambientVolumeSlider;
        [SerializeField] private UISettingsOptionToggle _subtitlesToggle;


        // OTHER FIELDS

        public static readonly string SUBTITLES_PLAYER_PREFS_KEY = "SubtitlesMenuSetting";
        private static readonly float DEFAULT_VOLUME = 0.8f;
        private static readonly float VOLUME_COMPARISON_TOLERANCE = 0.01f;
        private static readonly bool DEFAULT_SUBTITLE_SETTING = true;
        private float _previousMasterSliderAmount;
        private float _previousMusicSliderAmount;
        private float _previousSfxSliderAmount;
        private float _previousDialogueSliderAmount;
        private float _previousAmbientSliderAmount;
        private bool _previousSubtitlesToggleState;


        // INJECTION

        protected IGameVolume _gameVolume;

        [Inject]
        public virtual void Inject(IGameVolume gameVolume)
        {
            _gameVolume = gameVolume;
        }


        // PROPERTIES

        public override bool HasChanges => IsAudioSettingsChanged();
        public override Selectable FirstSelectable => DefaultSelectable;
        public override Selectable DefaultSelectable => _masterVolumeSlider;
        public override SettingsSubMenusEnum SettingsSubMenu => SettingsSubMenusEnum.Audio;


        // LIFECYCLE EVENTS

        private void Start()
        {
            InitializeMasterSlider();
            InitializeMusicSlider();
            InitializeSFXSlider();
            InitializeDialogueSlider();
            InitializeAmbientSlider();
            InitializeSubtitleToggle();

            SetupSelectableNavigation(transform);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

#if UNITY_EDITOR
            UnregisterEvents();//Unregister events in on destroy in case the execution is stopped while the setting menu is opened.
#endif
        }


        // EVENT HANDLING

        protected override void RegisterEvents()
        {
            base.RegisterEvents();

            _masterVolumeSlider.OnValueChangedPercentage += MasterVolumeSlider_OnChangedPercentage;
            _musicVolumeSlider.OnValueChangedPercentage += MusicVolumeSlider_OnChangedPercentage;
            _sfxVolumeSlider.OnValueChangedPercentage += SfxVolumeSlider_OnChangedPercentage;
            _dialogueVolumeSlider.OnValueChangedPercentage += DialogueVolumeSlider_OnChangedPercentage;
            _ambientVolumeSlider.OnValueChangedPercentage += AmbientVolumeSlider_OnChangedPercentage;
            _subtitlesToggle.OnValueChanged += SubtitlesToggle_OnValueChanged;

            _applySettingsButton.OnSubmitEvent += ApplySettings_OnSubmit;
            _discardSettingsButton.OnSubmitEvent += DiscardSettings_OnSubmit;
        }

        protected override void UnregisterEvents()
        {
            base.UnregisterEvents();

            _masterVolumeSlider.OnValueChangedPercentage -= MasterVolumeSlider_OnChangedPercentage;
            _musicVolumeSlider.OnValueChangedPercentage -= MusicVolumeSlider_OnChangedPercentage;
            _sfxVolumeSlider.OnValueChangedPercentage -= SfxVolumeSlider_OnChangedPercentage;
            _dialogueVolumeSlider.OnValueChangedPercentage -= DialogueVolumeSlider_OnChangedPercentage;
            _ambientVolumeSlider.OnValueChangedPercentage -= AmbientVolumeSlider_OnChangedPercentage;
            _subtitlesToggle.OnValueChanged -= SubtitlesToggle_OnValueChanged;


            _applySettingsButton.OnSubmitEvent -= ApplySettings_OnSubmit;
            _discardSettingsButton.OnSubmitEvent -= DiscardSettings_OnSubmit;
        }


        // OTHER METHODS

        public override void ResetToDefaults()
        {
            SetMasterSlider(DEFAULT_VOLUME);
            SetMusicSlider(DEFAULT_VOLUME);
            SetSfxSlider(DEFAULT_VOLUME);
            SetDialogueSlider(DEFAULT_VOLUME);
            SetAmbientSlider(DEFAULT_VOLUME);
            SetSubtitlesEnabled(DEFAULT_SUBTITLE_SETTING);

            ApplySettingsChanges();
        }

        public override void ApplySettingsChanges()
        {
            base.ApplySettingsChanges();

            _gameVolume.SaveVolumeSettings();

            _previousMasterSliderAmount = _gameVolume.GetMasterVolume();
            _previousMusicSliderAmount = _gameVolume.GetMusicVolume();
            _previousSfxSliderAmount = _gameVolume.GetSFXVolume();
            _previousDialogueSliderAmount = _gameVolume.GetDialogueVolume();

            int subtitlesEnabledValue = PlayerPrefs.GetInt(SUBTITLES_PLAYER_PREFS_KEY, defaultValue: -1);
            if (subtitlesEnabledValue != -1)
            {
                bool isSubtitlesEnabled = subtitlesEnabledValue == 1;
                _previousSubtitlesToggleState = isSubtitlesEnabled;
            }
        }

        public override void DiscardSettingsChanges()
        {
            base.DiscardSettingsChanges();

            SetMasterSlider(_previousMasterSliderAmount);
            SetMusicSlider(_previousMusicSliderAmount);
            SetSfxSlider(_previousSfxSliderAmount);
            SetDialogueSlider(_previousDialogueSliderAmount);
            SetAmbientSlider(_previousAmbientSliderAmount);
            SetSubtitlesEnabled(_previousSubtitlesToggleState);

            ApplySettingsChanges();
        }

        private void InitializeMasterSlider()
        {
            _previousMasterSliderAmount = _gameVolume.GetMasterVolume();
            _masterVolumeSlider.SetSliderAmountPercentage(_previousMasterSliderAmount);
        }

        private void InitializeMusicSlider()
        {
            _previousMusicSliderAmount = _gameVolume.GetMusicVolume();
            _musicVolumeSlider.SetSliderAmountPercentage(_previousMusicSliderAmount);
        }

        private void InitializeSFXSlider()
        {
            _previousSfxSliderAmount = _gameVolume.GetSFXVolume();
            _sfxVolumeSlider.SetSliderAmountPercentage(_previousSfxSliderAmount);
        }

        private void InitializeDialogueSlider()
        {
            _previousDialogueSliderAmount = _gameVolume.GetDialogueVolume();
            _dialogueVolumeSlider.SetSliderAmountPercentage(_previousDialogueSliderAmount);
        }

        private void InitializeAmbientSlider()
        {
            _previousAmbientSliderAmount = _gameVolume.GetAmbientVolume();
            _ambientVolumeSlider.SetSliderAmountPercentage(_previousAmbientSliderAmount);
        }

        private void InitializeSubtitleToggle()
        {
            bool isSubtitlesEnabled = PlayerPrefs.GetInt(SUBTITLES_PLAYER_PREFS_KEY, 1) == 1; // default is on
            _previousSubtitlesToggleState = isSubtitlesEnabled;
            _subtitlesToggle.SetToggleValue(isSubtitlesEnabled);
        }

        private void SetMasterSlider(float volume)
        {
            _masterVolumeSlider.SetSliderAmountPercentage(volume);
            _gameVolume.SetMasterVolume(volume);
        }

        private void SetSfxSlider(float volume)
        {
            _sfxVolumeSlider.SetSliderAmountPercentage(volume);
            _gameVolume.SetSFXVolume(volume);
        }

        private void SetMusicSlider(float volume)
        {
            _musicVolumeSlider.SetSliderAmountPercentage(volume);
            _gameVolume.SetMusicVolume(volume);
        }

        private void SetDialogueSlider(float volume)
        {
            _dialogueVolumeSlider.SetSliderAmountPercentage(volume);
            _gameVolume.SetDialogueVolume(volume);
        }

        private void SetAmbientSlider(float volume)
        {
            _ambientVolumeSlider.SetSliderAmountPercentage(volume);
            _gameVolume.SetAmbientVolume(volume);
        }

        private void SetSubtitlesEnabled(bool isSubtitlesEnabled)
        {
            PlayerPrefs.SetInt(SUBTITLES_PLAYER_PREFS_KEY, isSubtitlesEnabled ? 1 : 0);
            _subtitlesToggle.SetToggleValueNoNotify(isSubtitlesEnabled);
        }

        private void MasterVolumeSlider_OnChangedPercentage(float updatedMasterVolume)
        {
            _gameVolume.SetMasterVolume(updatedMasterVolume);
            bool isMasterVolumeDirty = !_previousMasterSliderAmount.Approx(updatedMasterVolume, VOLUME_COMPARISON_TOLERANCE);
            _masterVolumeSlider.SetDirtyFlag(isMasterVolumeDirty);
        }

        private void MusicVolumeSlider_OnChangedPercentage(float updatedMusicVolume)
        {
            _gameVolume.SetMusicVolume(updatedMusicVolume);
            bool isMusicVolumeDirty = !_previousMusicSliderAmount.Approx(updatedMusicVolume, VOLUME_COMPARISON_TOLERANCE);
            _musicVolumeSlider.SetDirtyFlag(isMusicVolumeDirty);
        }

        private void SfxVolumeSlider_OnChangedPercentage(float updatedSFXVolume)
        {
            _gameVolume.SetSFXVolume(updatedSFXVolume);
            bool isSFXVolumeDirty = !_previousSfxSliderAmount.Approx(updatedSFXVolume, VOLUME_COMPARISON_TOLERANCE);
            _sfxVolumeSlider.SetDirtyFlag(isSFXVolumeDirty);
        }

        private void DialogueVolumeSlider_OnChangedPercentage(float updatedDialogueVolume)
        {
            _gameVolume.SetDialogueVolume(updatedDialogueVolume);
            bool isDialogueVolumeDirty = !_previousDialogueSliderAmount.Approx(updatedDialogueVolume, VOLUME_COMPARISON_TOLERANCE);
            _dialogueVolumeSlider.SetDirtyFlag(isDialogueVolumeDirty);
        }

        private void AmbientVolumeSlider_OnChangedPercentage(float updatedAmbientVolume)
        {
            _gameVolume.SetAmbientVolume(updatedAmbientVolume);
            bool isAmbientVolumeDirty = !_previousAmbientSliderAmount.Approx(updatedAmbientVolume, VOLUME_COMPARISON_TOLERANCE);
            _ambientVolumeSlider.SetDirtyFlag(isAmbientVolumeDirty);
        }

        private void SubtitlesToggle_OnValueChanged(bool isSubtitlesEnabled)
        {
            int subtitlesValue = isSubtitlesEnabled ? 1 : 0;
            PlayerPrefs.SetInt(SUBTITLES_PLAYER_PREFS_KEY, subtitlesValue);

            _subtitlesToggle.SetDirtyFlag(_previousSubtitlesToggleState != isSubtitlesEnabled);
        }

        protected virtual bool IsAudioSettingsChanged()
        {
            float currentMaster = _gameVolume.GetMasterVolume();
            float currentMusic = _gameVolume.GetMusicVolume();
            float currentSFX = _gameVolume.GetSFXVolume();
            float currentDialogue = _gameVolume.GetDialogueVolume();
            float currentAmbient = _gameVolume.GetDialogueVolume();
            bool isSubtitlesEnabled = PlayerPrefs.GetInt(SUBTITLES_PLAYER_PREFS_KEY, 1) == 1;

            bool isMasterUnchanged = _previousMasterSliderAmount.Approx(currentMaster, VOLUME_COMPARISON_TOLERANCE);
            bool isMusicUnchanged = _previousMusicSliderAmount.Approx(currentMusic, VOLUME_COMPARISON_TOLERANCE);
            bool isSFXUnchanged = _previousSfxSliderAmount.Approx(currentSFX, VOLUME_COMPARISON_TOLERANCE);
            bool isDialogueUnchanged = _previousDialogueSliderAmount.Approx(currentDialogue, VOLUME_COMPARISON_TOLERANCE);
            bool isAmbientUnchanged = _previousAmbientSliderAmount.Approx(currentAmbient, VOLUME_COMPARISON_TOLERANCE);
            bool isSubtitlesUnchanged = _previousSubtitlesToggleState == isSubtitlesEnabled;

            return !isMasterUnchanged || !isMusicUnchanged || !isSFXUnchanged || !isDialogueUnchanged ||
                   !isAmbientUnchanged || !isSubtitlesUnchanged;
        }
    }
}