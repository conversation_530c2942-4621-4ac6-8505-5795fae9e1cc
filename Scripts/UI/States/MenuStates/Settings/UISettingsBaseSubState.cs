// Copyright Isto Inc.

#if PLATFORM_GAMECORE
using System.Linq;
#endif

using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    ///<summary>
    /// An abstract class managing UI settings sub-states. It holds controls for applying, discarding and resetting
    /// settings. Detects user's interactions and provides an interface for handling these actions in child classes.
    /// </summary>
    public abstract class UISettingsBaseSubState : MonoState
    {
        public enum TabDirection
        {
            Left,
            Right
        }


        // UNITY HOOKUP

        [Header("UI Component Hookups")]
        [SerializeField] protected CanvasGroup _canvasGroup;

        [Header("Confirm Dialogue")]
        [SerializeField] protected UISimpleConfirmModalState _confirmationSubState;

        [Header("Settings Apply/Discard Buttons")]
        [SerializeField] protected CoreButton _applySettingsButton;
        [SerializeField] protected CoreButton _defaultsSettingsButton;
        [SerializeField] protected CoreButton _discardSettingsButton;

        [<PERSON><PERSON>("Tab Button")]
        [FormerlySerializedAs("_defaultSubMenusButton")]
        [SerializeField] protected CoreButton _tabButton;
        [SerializeField] protected CoreButtonHighlightableController _tabButtonHighlighter;

        [Header("Xbox Specific")]
        [SerializeField] private bool _removeFromXboxBuild = false;


        // OTHER FIELDS

        private static readonly float MAX_VERTICAL_ANGLE = 0.35f;
        private static readonly float MIN_HORIZONTAL_BUFFER = 0.85f;

        protected MonoPushdownStateMachine _menuController;
        protected UISettingsSelectable[] _selectableSettings;

        //TODO: JP.2024-02-27 - Remove this hardcoded value when we are done with Atrio
        private static readonly string NORTH_BUTTON_RAW_INPUT_NAME = "joystick button 3";

        private bool _isHorizontalAxisInUse;
        private float _cooldownAtSateEnter;
        private bool _isButtonHeldFromPreviousState;
        private UISettingsSelectable _lastSelectableInSubState;
        private Selectable _previousSelectionMade;


        // PROPERTIES

        private static GameObject CurrentSelection => EventSystem.current.currentSelectedGameObject;

        public abstract bool HasChanges { get; }
        /// <summary>
        /// This is the first selectable from the top, as far as navigation is concerned
        /// </summary>
        public abstract Selectable FirstSelectable { get; }
        /// <summary>
        /// This is the preferred starting selection for indirect means of navigation or for entering controller mode.
        /// </summary>
        public abstract Selectable DefaultSelectable { get; }
        public abstract SettingsSubMenusEnum SettingsSubMenu { get; }

        public virtual bool RequiresUserValidation => false;

        public bool KeepNavInStateButtonsBar { get; set; }

        /// <summary>
        /// The button from the tab that opens this menu page.
        /// </summary>
        public CoreButton TabButton => _tabButton;

        /// <summary>
        /// Determines if this substate should be removed from the xbox build
        /// </summary>
        public bool RemoveFromXboxBuild => _removeFromXboxBuild;

        /// <summary>
        /// The highlight controller from the button of our tab.
        /// </summary>
        private CoreButtonHighlightableController TabButtonHighlighter
        {
            get
            {
                if(_tabButtonHighlighter == null)
                {
                    _tabButtonHighlighter = _tabButton.GetComponent<CoreButtonHighlightableController>();
                }
                return _tabButtonHighlighter;
            }
        }


        // EVENTS

        public event Action<bool> OnSubStateAttemptClose;
        public event Action OnResetToDefaultsSubmit;
        public event Action OnResetAllToDefaultsSubmit;
        public event Action<TabDirection> OnTabDirection;
        public event Action OnSubMenuCategoryChanged;


        // INJECTION

        private IControls _controls;

        [Inject]
        public void Inject(IControls controls)
        {
            _controls = controls;
        }


        // LIFECYCLE EVENTS

        protected virtual void OnDestroy()
        {
#if UNITY_EDITOR
            // Unregister events in OnDestroy in case of exiting Play Mode while the setting menu is opened.
            UnregisterEvents();
#endif
        }

        public override void Enter(MonoStateMachine controller)
        {
            _menuController = controller as MonoPushdownStateMachine;
            _cooldownAtSateEnter = Constants.BUTTON_SPAM_DELAY;

            _canvasGroup.alpha = 1f;
            _canvasGroup.interactable = true;
            _canvasGroup.blocksRaycasts = true;
            gameObject.SetActive(true);
            _isButtonHeldFromPreviousState = false;

            TabButtonHighlighter?.SetHighlighted(true);
            SetSelectionToDefaultSelectable();
            SetupLastSelectable();
            RegisterEvents();
        }

        public override void Exit(MonoStateMachine controller)
        {
            TabButtonHighlighter?.SetHighlighted(false);
            DeselectAllSelectableSettings();
            UnregisterEvents();
            gameObject.SetActive(false);
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            _cooldownAtSateEnter = Constants.BUTTON_SPAM_DELAY;

            SetSelectionToPrevious();

            // There is an edge case with the controls being changed. Going into the ReMapping sub state and changing the
            // controls to the B button (i.e. UserActions.UICANCEL) then the menu will close out since we are
            // checking for GetButtonDown. This flag ensures that we take this into buttons being pressed from other
            // sub states.
            if (_controls.GetAnyButton())
            {
                _isButtonHeldFromPreviousState = true;
            }
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            // Need to block our Run logic completely here because even disabling the controls doesn't prevent this
            // GetButtonDown check from falsely triggering if we enter while the button press is happening from somewhere else
            _cooldownAtSateEnter -= Time.unscaledDeltaTime;
            if (_cooldownAtSateEnter > 0f)
            {
                return null; // return value is irrelevant when running in MonoPushdownStateMachine
            }

            SettingsNavigation();
            VerifyApplyDiscardButtons();

            return null; // return value is irrelevant when running in MonoPushdownStateMachine
        }


        // EVENT HANDLING

        protected virtual void RegisterEvents()
        {
            _defaultsSettingsButton.OnSubmitEvent += DefaultsButton_OnSubmit;
            _applySettingsButton.OnSubmitEvent += ApplySettings_OnSubmit;
            _discardSettingsButton.OnSubmitEvent += DiscardSettings_OnSubmit;
            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        protected virtual void UnregisterEvents()
        {
            _applySettingsButton.OnSubmitEvent -= ApplySettings_OnSubmit;
            _discardSettingsButton.OnSubmitEvent -= DiscardSettings_OnSubmit;
            _defaultsSettingsButton.OnSubmitEvent -= DefaultsButton_OnSubmit;
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        protected virtual void ApplySettings_OnSubmit()
        {
            ApplySettingsChanges();
            _defaultsSettingsButton.Select();
        }

        protected void DiscardSettings_OnSubmit()
        {
            DiscardSettingsChanges();
            _defaultsSettingsButton.Select();
        }

        private void DefaultsButton_OnSubmit()
        {
            _previousSelectionMade = _defaultsSettingsButton;
            OnResetToDefaultsSubmit?.Invoke();
        }

        private void Events_OnInputModeChanged()
        {
            if (_previousSelectionMade == null)
            {
                _previousSelectionMade = DefaultSelectable;
            }

            _previousSelectionMade.Select();
        }


        // ACCESSORS

        protected virtual void SetSelectionToDefaultSelectable()
        {
            if (!Controls.UsingController)
                return;

            if (KeepNavInStateButtonsBar && TabButton != null)
            {
                TabButton.Select();
            }
            else if (DefaultSelectable != null)
            {
                DefaultSelectable.Select();
            }
        }

        private void SetSelectionToPrevious()
        {
            if (_previousSelectionMade)
            {
                _previousSelectionMade.Select();
                _previousSelectionMade = null;
            }
        }


        // OTHER METHODS

        /// <summary>
        /// If you have settings that will require the user to see them after they are applied and then confirm
        /// if they are good, this method is where you would apply those.
        /// </summary>
        public virtual void ApplySettingsForUserValidation()
        {

        }

        public abstract void ResetToDefaults();

        public virtual void ApplySettingsChanges()
        {
            PlayerPrefs.Save();
            Events.RaiseEvent(Events.SETTINGS_SAVED);
            ClearSelectablesDirtyFlag();
        }

        public virtual void DiscardSettingsChanges()
        {
            ClearSelectablesDirtyFlag();
        }

        protected void SetupSelectableNavigation(Transform selectablesContainer)
        {
            _selectableSettings = selectablesContainer.GetComponentsInChildren<UISettingsSelectable>();

#if PLATFORM_GAMECORE
            FilterOutNonXboxSettings();
#endif

            if (_selectableSettings.Length == 0)
            {
                Debug.LogWarning("Warning: There are no selectable options in this menu.");
            }

            if (_selectableSettings.Length == 1)
            {
                _lastSelectableInSubState = _selectableSettings[0];
                SetupLastSelectable();
                return;
            }

            for (int i = 0; i < _selectableSettings.Length; i++)
            {
                if (i == _selectableSettings.Length - 1)
                {
                    UIUtils.SetNavigation(_selectableSettings[i], UIUtils.NavigationDirection.Up,
                        _selectableSettings[i - 1]);

                    _lastSelectableInSubState = _selectableSettings[i];
                    SetupLastSelectable();
                    continue;
                }

                UIUtils.SetNavigation(_selectableSettings[i], UIUtils.NavigationDirection.Down,
                    _selectableSettings[i + 1]);

                if (i == 0)
                {
                    continue;
                }

                UIUtils.SetNavigation(_selectableSettings[i], UIUtils.NavigationDirection.Up,
                    _selectableSettings[i - 1]);
            }
        }

        protected virtual void VerifyApplyDiscardButtons()
        {
            if (HasChanges != _applySettingsButton.interactable)
            {
                _applySettingsButton.interactable = HasChanges;
                _applySettingsButton.enabled = HasChanges;
            }

            if (HasChanges != _discardSettingsButton.interactable)
            {
                _discardSettingsButton.interactable = HasChanges;
                _discardSettingsButton.enabled = HasChanges;
            }
        }

        protected void InvokeTabDirection(TabDirection tabDirection)
        {
            OnTabDirection?.Invoke(tabDirection);
        }

        protected void InvokeSubCategoryChanged()
        {
            OnSubMenuCategoryChanged?.Invoke();
        }

        private void HandleDirectionChanges(float horizontalValue, float verticalValue)
        {
            if (EventSystem.current.currentSelectedGameObject == null)
                return;

            UISettingsSelectable currentSelection = CurrentSelection.GetComponent<UISettingsSelectable>();

            if (currentSelection)
            {
                float absoluteHorizontal = Mathf.Abs(horizontalValue);
                float absoluteVertical = Mathf.Abs(verticalValue);
                bool isEligibleForHorizontalMovement = !_isHorizontalAxisInUse
                                                    && absoluteHorizontal > absoluteVertical
                                                    && absoluteHorizontal >= MIN_HORIZONTAL_BUFFER
                                                    && absoluteVertical <= MAX_VERTICAL_ANGLE;

                if (isEligibleForHorizontalMovement)
                {
                    InvokeDirectionChange(horizontalValue);
                    _isHorizontalAxisInUse = true;
                }
                else if (absoluteHorizontal == 0)
                {
                    _isHorizontalAxisInUse = false;
                }
            }
        }

        private void InvokeDirectionChange(float horizontalValue)
        {
            UISettingsSelectable currentSelectable = CurrentSelection.GetComponent<UISettingsSelectable>();

            if (Mathf.Sign(horizontalValue) == 1f)
            {
                currentSelectable.HandleRightInput();
            }
            else
            {
                currentSelectable.HandleLeftInput();
            }
        }

        private void ClearSelectablesDirtyFlag()
        {
            if (_selectableSettings == null)
                return;

            foreach (UISettingsSelectable selectableSetting in _selectableSettings)
            {
                selectableSetting.SetDirtyFlag(false);
            }
        }

        private void DeselectAllSelectableSettings()
        {
            if (_selectableSettings == null)
                return;

            foreach (UISettingsSelectable currentSelectable in _selectableSettings)
            {
                currentSelectable.SetSelection(false);
            }
        }

        private void SettingsNavigation()
        {
            float horizontalValue = _controls.GetAxis(Controls.MovementAxis.UIHorizontal);
            float verticalValue = _controls.GetAxis(Controls.MovementAxis.UIVertical);

            HandleDirectionChanges(horizontalValue, verticalValue);

            if (_controls.GetButtonUp(UserActions.UICANCEL))
            {
                // Check if any button is being held from the previous sub state. We don't want to apply any input
                // from a previous sub state onto the current one.
                if (!_isButtonHeldFromPreviousState)
                {
                    // If there have been no changes when we attempt to close out, we exit out of the substate
                    if (!HasChanges)
                    {
                        _menuController.ExitSubState();
                    }

                    // The fact that we are attempting to close the substate, whether there are changes or not, is notified
                    OnSubStateAttemptClose?.Invoke(HasChanges);
                }
            }

            if (_isButtonHeldFromPreviousState && !_controls.GetAnyButton())
            {
                _isButtonHeldFromPreviousState = false;
            }

            if (_controls.GetButtonDown(UserActions.UITABLEFT))
            {
                InvokeTabDirection(TabDirection.Left);
            }
            else if (_controls.GetButtonDown(UserActions.UITABRIGHT))
            {
                InvokeTabDirection(TabDirection.Right);
            }

            if (_controls.GetButtonDown(UserActions.UISETTINGSRESETALLTODEFAULTS))
            {
                CacheCurrentSelection();

                OnResetAllToDefaultsSubmit?.Invoke();
            }

        }

        private void CacheCurrentSelection()
        {
            if (CurrentSelection != null)
            {
                Selectable updatedSelection = CurrentSelection.GetComponent<Selectable>();

                if (updatedSelection)
                {
                    _previousSelectionMade = updatedSelection;
                }
            }
        }

        private void SetupLastSelectable()
        {
            if (_lastSelectableInSubState == null)
            {
                return;
            }

            UIUtils.SetNavigation(_lastSelectableInSubState, UIUtils.NavigationDirection.Down, _defaultsSettingsButton);
            UIUtils.SetNavigation(_discardSettingsButton, UIUtils.NavigationDirection.Up, _lastSelectableInSubState);
            UIUtils.SetNavigation(_defaultsSettingsButton, UIUtils.NavigationDirection.Up, _lastSelectableInSubState);
            UIUtils.SetNavigation(_applySettingsButton, UIUtils.NavigationDirection.Up, _lastSelectableInSubState);
        }

#if PLATFORM_GAMECORE
        private void FilterOutNonXboxSettings()
        {
            if (Debug.isDebugBuild)
                return;

            // Find settings to be removed from the Xbox build
            UISettingsSelectable[] nonXboxSettings = _selectableSettings
                .Where(settingsSelectable => settingsSelectable.RemoveFromXboxBuild)
                .ToArray();

            // Deactivate or destroy the non-Xbox settings
            foreach (UISettingsSelectable currentSetting in nonXboxSettings)
            {
                // NOTE: We can choose to destroy the navigation objects and pages here if that is preferred
                currentSetting.gameObject.SetActive(false);
            }

            // Update _selectableSettings to exclude the non-Xbox settings
            _selectableSettings = _selectableSettings
                .Where(settingsSelectable => !settingsSelectable.RemoveFromXboxBuild)
                .ToArray();
        }
#endif
    }
}