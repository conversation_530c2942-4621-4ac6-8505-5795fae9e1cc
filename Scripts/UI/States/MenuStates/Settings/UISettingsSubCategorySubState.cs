// Copyright Isto Inc.

using UnityEngine;
using UnityEngine.UI;

namespace Isto.Core.UI
{
    /// <summary>
    /// A sub category within a settings sub state. The sub categories are filtered out from the Menu State as they
    /// do not control tab navigation.
    /// </summary>
    public class UISettingsSubCategorySubState : UISettingsBaseSubState
    {
        [Header("Substate Tab Button")]
        [SerializeField] protected CoreButtonHighlightableController _substateTabButtonHighlighter;

        public override bool HasChanges { get; }
        public override Selectable FirstSelectable { get; }
        public override Selectable DefaultSelectable { get; }
        public override SettingsSubMenusEnum SettingsSubMenu { get; }
        public override void ResetToDefaults() { }

        /// <summary>
        /// The button from the tab that opens this submenu page.
        /// </summary>
        public CoreButton SubTabButton => _substateTabButtonHighlighter.GetButton();

        /// <summary>
        /// The highlight controller from the button of our submenu tab.
        /// </summary>
        private CoreButtonHighlightableController SubTabButtonHighlighter => _substateTabButtonHighlighter;
    }
}