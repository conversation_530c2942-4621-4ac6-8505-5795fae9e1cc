// Copyright Isto Inc.

using I2.Loc;
using Isto.Core.Cheats;
using Isto.Core.Data;
using Isto.Core.Installers;
using Isto.Core.Localization;
using Isto.Core.Speedrun;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// Sub-state class for managing the games gameplay settings.
    /// </summary>
    public class UISettingsGameplaySubState : UISettingsBaseSubState
    {
        // UNITY HOOKUP

        [Header("Gameplay Settings")]
        [SerializeField] private UISettingsOptionCarousel _languageCarousel;
        [SerializeField] private UISettingsOptionCarousel _autoSaveFrequencyCarousel;
        [SerializeField] private UISettingsOptionToggle _cheatMenuToggle;
        [SerializeField] private UISettingsOptionToggle _speedRunToggle;
        [FormerlySerializedAs("_speedrunStyle")]
        [SerializeField] private UISettingsOptionCarousel _speedrunConfig;
        [SerializeField] private UISettingsOptionSlider _timerTopToBottomPosition;
        [SerializeField] private UISettingsOptionSlider _timerLeftToRightPosition;


        [Header("Auto Save Localization")]
        [SerializeField] private LocalizedString _settingNever;
        [SerializeField] private LocalizedString _minutesSuffix;


        // OTHER FIELDS

        public static readonly string CHEAT_MENU_PLAYER_PREFS_KEY = "CheatMenuSetting";

        private static readonly bool DEFAULT_CHEAT_MENU_SETTING = false;
        private static readonly int DEFAULT_SAVE_FREQUENCY = 2;

        private List<LocExpression> _currentAutoSaveOptions = new List<LocExpression>();
        private List<LocExpression> _currentLanguages = new List<LocExpression>();
        private List<LocExpression> _currentTimerConfigs = new List<LocExpression>();
        private bool _previousCheatMenuAllowed;
        private LocExpression _previousLanguage;
        private int _previousSaveFrequency;
        private bool _previousSpeedrunMenuAllowed;
        private float _previousSpeedrunVerticalPos;
        private float _previousSpeedrunHorizontalPos;
        private int _previousSpeedrunConfig;


        // PROPERTIES

        public override bool HasChanges => IsGameplaySettingsChanged();
        public override Selectable FirstSelectable => DefaultSelectable;
        public override Selectable DefaultSelectable => _languageCarousel;
        public override SettingsSubMenusEnum SettingsSubMenu => SettingsSubMenusEnum.Gameplay;


        // INJECTION

        protected ILocalizationProvider _localization;
        private CheatSettings _cheatSettings;

        protected LanguageList _supportedLanguages;
        protected LocTerm.Factory _localizedStringFactory;
        protected Configuration.Settings _settings;

        // optional
        private SpeedrunSettings _speedrunSettings;

        [Inject]
        public virtual void Inject(ILocalizationProvider localization, CheatSettings devConsole,
            [Inject(Id = InjectId.SUPPORTED_LANGUAGES)] LanguageList supportedLanguages,
            LocTerm.Factory localizedFactory, Configuration.Settings settings,
            [InjectOptional] SpeedrunSettings speedrunSettings)
        {
            _localization = localization;
            _cheatSettings = devConsole;

            _supportedLanguages = supportedLanguages;
            _localizedStringFactory = localizedFactory;

            _settings = settings;

            _speedrunSettings = speedrunSettings;
        }


        // LIFECYCLE EVENTS

        protected virtual void Awake()
        {
            if (_cheatMenuToggle != null && !_cheatSettings.CheatsEnabled)
            {
                _cheatMenuToggle.gameObject.SetActive(false);
            }
        }

        protected virtual void Start()
        {
            InitializeLanguageCarousel();
            InitializeAutoSaveFrequencyCarousel();
            InitializeCheatMenuToggle();
            InitializeSpeedRunSection();

            SetupSelectableNavigation(transform);
        }


        // EVENT HANDLING

        protected override void RegisterEvents()
        {
            base.RegisterEvents();

            _languageCarousel.OnValueChanged += LanguageCarousel_OnValueChanged;
            _autoSaveFrequencyCarousel.OnValueChanged += AutoSaveCarousel_OnValueChanged;
            _cheatMenuToggle.OnValueChanged += CheatMenuToggle_OnValueChanged;
            _speedRunToggle.OnValueChanged += SpeedRunToggle_OnValueChanged;
            _speedrunConfig.OnValueChanged += SpeedRunCarousel_OnValueChanged;
            _timerTopToBottomPosition.OnValueChangedPercentage += TimerVerticalPosSlider_OnValueChanged;
            _timerLeftToRightPosition.OnValueChangedPercentage += TimerHorizontalPosSlider_OnValueChanged;
        }

        protected override void UnregisterEvents()
        {
            base.UnregisterEvents();

            _languageCarousel.OnValueChanged -= LanguageCarousel_OnValueChanged;
            _autoSaveFrequencyCarousel.OnValueChanged -= AutoSaveCarousel_OnValueChanged;
            _cheatMenuToggle.OnValueChanged -= CheatMenuToggle_OnValueChanged;
            _speedRunToggle.OnValueChanged -= SpeedRunToggle_OnValueChanged;
            _speedrunConfig.OnValueChanged -= SpeedRunCarousel_OnValueChanged;
            _timerTopToBottomPosition.OnValueChangedPercentage -= TimerVerticalPosSlider_OnValueChanged;
            _timerLeftToRightPosition.OnValueChangedPercentage -= TimerHorizontalPosSlider_OnValueChanged;
        }

        private void LanguageCarousel_OnValueChanged(LocExpression newLanguage)
        {
            SingleTermLocExpression newLanguageLoc = newLanguage as SingleTermLocExpression;
            LocTerm languageTerm = newLanguageLoc.GetTerm();
            var language = LanguageChanger.LanguageEnum.GetFromName(languageTerm.GetKey()) as LanguageChanger.LanguageEnum;

            _localization.SetLanguage(language);

            _languageCarousel.SetDirtyFlag(!_previousLanguage.Equals(newLanguage));
        }

        private void AutoSaveCarousel_OnValueChanged(LocExpression newAutoSaveInterval)
        {
            int autoSaveIndex = _currentAutoSaveOptions.IndexOf(newAutoSaveInterval);
            AutoSaveManager.SetAutoSaveFrequencySetting(autoSaveIndex);

            _autoSaveFrequencyCarousel.SetDirtyFlag(_previousSaveFrequency != AutoSaveManager.GetAutoSaveFrequencySetting());
        }

        private void CheatMenuToggle_OnValueChanged(bool isCheatMenuEnabled)
        {
            int cheatMenuValue = isCheatMenuEnabled ? 1 : 0;
            PlayerPrefs.SetInt(CHEAT_MENU_PLAYER_PREFS_KEY, cheatMenuValue);

            _cheatMenuToggle.SetDirtyFlag(_previousCheatMenuAllowed != isCheatMenuEnabled);
        }

        private void SpeedRunToggle_OnValueChanged(bool isSpeedRunEnabled)
        {
            int speedRunValue = isSpeedRunEnabled ? 1 : 0;
            PlayerPrefs.SetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY, speedRunValue);

            _speedRunToggle.SetDirtyFlag(_previousSpeedrunMenuAllowed != isSpeedRunEnabled);
        }

        private void SpeedRunCarousel_OnValueChanged(LocExpression value)
        {
            SingleTermLocExpression localizedConfigExp = value as SingleTermLocExpression;
            LocTerm configName = localizedConfigExp.GetTerm();
            int configIndex = _speedrunSettings.GetTimerConfigIndex(configName.GetKey());
            if (configIndex != -1)
            {
                PlayerPrefs.SetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, configIndex);
                _speedrunConfig.SetDirtyFlag(_previousSpeedrunConfig != configIndex);
            }
        }

        private void TimerVerticalPosSlider_OnValueChanged(float pos)
        {
            PlayerPrefs.SetFloat(SpeedrunSettings.VERTICAL_POS_PLAYER_PREFS_KEY, pos);
            _timerTopToBottomPosition.SetDirtyFlag(!_previousSpeedrunVerticalPos.Approx(pos));
        }

        private void TimerHorizontalPosSlider_OnValueChanged(float pos)
        {
            PlayerPrefs.SetFloat(SpeedrunSettings.HORIZONTAL_POS_PLAYER_PREFS_KEY, pos);
            _timerLeftToRightPosition.SetDirtyFlag(!_previousSpeedrunHorizontalPos.Approx(pos));
        }

        protected virtual bool IsGameplaySettingsChanged()
        {
            bool isCheatMenuEnabled = PlayerPrefs.GetInt(CHEAT_MENU_PLAYER_PREFS_KEY, 0) == 1;
            bool isSpeedRunEnabled = PlayerPrefs.GetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY, 0) == 1;
            float topToBottom = PlayerPrefs.GetFloat(SpeedrunSettings.VERTICAL_POS_PLAYER_PREFS_KEY, defaultValue: 0f);
            float leftToRight = PlayerPrefs.GetFloat(SpeedrunSettings.HORIZONTAL_POS_PLAYER_PREFS_KEY, 0f);

            int defaultSpeedrunTimerConfig = _previousSpeedrunConfig;
            if (_speedrunSettings != null)
            {
                defaultSpeedrunTimerConfig = _speedrunSettings.GetDefaultTimerConfigIndex();
            }
            int style = PlayerPrefs.GetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, defaultValue: defaultSpeedrunTimerConfig);

            return _previousLanguage.Localize() != _localization.GetCurrentLanguageName()
                || _previousSaveFrequency != AutoSaveManager.GetAutoSaveFrequencySetting()
                || _previousCheatMenuAllowed != isCheatMenuEnabled
                || _previousSpeedrunMenuAllowed != isSpeedRunEnabled
                || _previousSpeedrunConfig != style
                || !_previousSpeedrunVerticalPos.Approx(topToBottom)
                || !_previousSpeedrunHorizontalPos.Approx(leftToRight);
        }


        // ACCESSORS

        private void SetLanguage(LocExpression newLanguage)
        {
            if (newLanguage.Localize() == _localization.GetCurrentLanguageName())
                return;

            var langEnumValue = LanguageChanger.LanguageEnum.GetFromName(newLanguage.Localize()) as LanguageChanger.LanguageEnum;
            _localization.SetLanguage(langEnumValue);
            _languageCarousel.SetCarouselSelectionNoNotify(newLanguage);
        }

        private void SetSaveFrequency(int newSaveFrequency)
        {
            AutoSaveManager.SetAutoSaveFrequencySetting(newSaveFrequency);
            _autoSaveFrequencyCarousel.SetCarouselSelectionNoNotify(newSaveFrequency);
        }

        private void SetCheatMenu(bool isCheatMenuEnabled)
        {
            PlayerPrefs.SetInt(CHEAT_MENU_PLAYER_PREFS_KEY, isCheatMenuEnabled ? 1 : 0);
            _cheatMenuToggle.SetToggleValueNoNotify(isCheatMenuEnabled);
        }

        private void SetSpeedrunMenu(bool isSpeedrunTimerEnabled)
        {
            PlayerPrefs.SetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY, isSpeedrunTimerEnabled ? 1 : 0);
            _speedRunToggle.SetToggleValueNoNotify(isSpeedrunTimerEnabled);
        }

        private void SetSpeedrunUIStyle(int speedrunStyle)
        {
            PlayerPrefs.SetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, speedrunStyle);
            _speedrunConfig.SetCarouselSelectionNoNotify(speedrunStyle);
        }

        private void SetSpeedrunUIPositionVertical(float pos)
        {
            PlayerPrefs.SetFloat(SpeedrunSettings.VERTICAL_POS_PLAYER_PREFS_KEY, pos);
            _timerTopToBottomPosition.SetSliderAmountPercentage(pos);
        }

        private void SetSpeedrunUIPositionHorizontal(float pos)
        {
            PlayerPrefs.SetFloat(SpeedrunSettings.HORIZONTAL_POS_PLAYER_PREFS_KEY, pos);
            _timerLeftToRightPosition.SetSliderAmountPercentage(pos);
        }

        // OTHER METHODS

        public override void ResetToDefaults()
        {
            // I talked with Stephen and it seems that reseting the language is not likely to be desired. -Frank
            //SetLanguage(ResetLanguageToSystem());

            SetSaveFrequency(DEFAULT_SAVE_FREQUENCY);
            SetCheatMenu(DEFAULT_CHEAT_MENU_SETTING);
            SetSpeedrunMenu(_speedrunSettings.GetDefaultTimerIsOnSetting());
            SetSpeedrunUIStyle(_speedrunSettings.GetDefaultTimerConfigIndex());
            SetSpeedrunUIPositionVertical(_speedrunSettings.GetDefaultScreenHeightRatio());
            SetSpeedrunUIPositionHorizontal(_speedrunSettings.GetDefaultScreenWidthRatio());

            ApplySettingsChanges();
        }

        private LocExpression ResetLanguageToSystem()
        {
            string currentDeviceLanguage = _localization.GetCurrentDeviceLanguage();
            LocExpression defaultLanguage = new SingleTermLocExpression(_localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, currentDeviceLanguage));

            if (!_currentLanguages.Contains(defaultLanguage))
            {
                defaultLanguage = _currentLanguages[0];
            }

            return defaultLanguage;
        }

        public override void ApplySettingsChanges()
        {
            base.ApplySettingsChanges();

            LocTerm currentLanguage = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, _localization.GetCurrentLanguageName());
            _previousLanguage = new SingleTermLocExpression(currentLanguage);
            _previousSaveFrequency = AutoSaveManager.GetAutoSaveFrequencySetting();

            int cheatMenuEnabledValue = PlayerPrefs.GetInt(CHEAT_MENU_PLAYER_PREFS_KEY, defaultValue: -1);
            if (cheatMenuEnabledValue != -1)
            {
                bool isCheatMenuEnabled = cheatMenuEnabledValue == 1;
                _previousCheatMenuAllowed = isCheatMenuEnabled;
            }

            int speedrunEnabledValue = PlayerPrefs.GetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY, defaultValue: -1);
            if (speedrunEnabledValue != -1)
            {
                bool isSpeedRunEnabled = speedrunEnabledValue == 1;
                _previousSpeedrunMenuAllowed = isSpeedRunEnabled;
            }

            int speedrunTimerStyle = PlayerPrefs.GetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, defaultValue: -1);
            if (speedrunTimerStyle != -1)
            {
                _previousSpeedrunConfig = speedrunTimerStyle;
            }

            float topToBottom = PlayerPrefs.GetFloat(SpeedrunSettings.VERTICAL_POS_PLAYER_PREFS_KEY, defaultValue: -1f);
            if (topToBottom > -0.1f)
            {
                _previousSpeedrunVerticalPos = topToBottom;
            }

            float leftToRight = PlayerPrefs.GetFloat(SpeedrunSettings.HORIZONTAL_POS_PLAYER_PREFS_KEY, defaultValue: -1f);
            if (leftToRight > -0.1f)
            {
                _previousSpeedrunHorizontalPos = leftToRight;
            }
        }

        public override void DiscardSettingsChanges()
        {
            base.DiscardSettingsChanges();

            SetLanguage(_previousLanguage);
            SetSaveFrequency(_previousSaveFrequency);
            SetCheatMenu(_previousCheatMenuAllowed);
            SetSpeedrunMenu(_previousSpeedrunMenuAllowed);
            SetSpeedrunUIStyle(_previousSpeedrunConfig);
            SetSpeedrunUIPositionVertical(_previousSpeedrunVerticalPos);
            SetSpeedrunUIPositionHorizontal(_previousSpeedrunHorizontalPos);

            ApplySettingsChanges();
        }

        private void InitializeLanguageCarousel()
        {
            _currentLanguages = new List<LocExpression>(_supportedLanguages.GetLanguageNames().Select(language
                => new SingleTermLocExpression(_localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, language))).ToList());

            LocTerm currentLanguage = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, _localization.GetCurrentLanguageName());
            _previousLanguage = new SingleTermLocExpression(currentLanguage);

            _languageCarousel.SetCarouselList(_currentLanguages, _previousLanguage);
        }

        private void InitializeAutoSaveFrequencyCarousel()
        {
            int currentSelection = 0;
            _currentAutoSaveOptions = new List<LocExpression>();
            foreach (int currentAutoSaveOption in _settings.AutoSaveOptions)
            {
                if (currentAutoSaveOption == 0)
                {
                    LocTerm never = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, _settingNever.mTerm);
                    _currentAutoSaveOptions.Add(new SingleTermLocExpression(never));
                }
                else
                {
                    LocTerm saveOption = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, $"{currentAutoSaveOption}");
                    LocTerm suffix = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, _minutesSuffix.mTerm);
                    LocTerm separator = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, " ");

                    _currentAutoSaveOptions.Add(new RepositionableLocExpression(saveOption, suffix, separator, LocTermAffixPositionEnum.SUFFIX));
                }
            }

            _previousSaveFrequency = AutoSaveManager.GetAutoSaveFrequencySetting();
            _autoSaveFrequencyCarousel.SetCarouselList(_currentAutoSaveOptions, AutoSaveManager.GetAutoSaveFrequencySetting());
        }

        private void InitializeCheatMenuToggle()
        {
            bool isCheatMenuEnabled = PlayerPrefs.GetInt(CHEAT_MENU_PLAYER_PREFS_KEY, 0) == 1; // default is off
            _previousCheatMenuAllowed = isCheatMenuEnabled;
            _cheatMenuToggle.SetToggleValue(isCheatMenuEnabled);
        }

        private void InitializeSpeedRunSection()
        {
            bool displayToggle = false;
            bool displaySliders = false;
            bool displayCarousel = false;

            if (_speedrunSettings)
            {
                displayToggle = _speedrunSettings.CanUserToggleTimer();
                displaySliders = _speedrunSettings.CanUserChangeTimerPosition();
                displayCarousel = _speedrunSettings.CanUserChangeTimerStyle();
            }

            _speedRunToggle.gameObject.SetActive(displayToggle);
            PlayerPrefs.SetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY, displayToggle ? 1 : 0);
            _previousSpeedrunMenuAllowed = displayToggle;
            _speedRunToggle.SetToggleValue(displayToggle);

            if (displaySliders)
            {
                if (!_timerTopToBottomPosition.gameObject.activeSelf)
                {
                    _timerTopToBottomPosition.gameObject.SetActive(true);
                }
                if (!_timerLeftToRightPosition.gameObject.activeSelf)
                {
                    _timerLeftToRightPosition.gameObject.SetActive(true);
                }

                float topToBottom = _speedrunSettings.GetDefaultScreenHeightRatio();
                float leftToRight = _speedrunSettings.GetDefaultScreenWidthRatio();

                topToBottom = PlayerPrefs.GetFloat(SpeedrunSettings.VERTICAL_POS_PLAYER_PREFS_KEY, defaultValue: topToBottom);
                leftToRight = PlayerPrefs.GetFloat(SpeedrunSettings.HORIZONTAL_POS_PLAYER_PREFS_KEY, defaultValue: leftToRight);

                _previousSpeedrunVerticalPos = topToBottom;
                _timerTopToBottomPosition.SetSliderAmountPercentage(topToBottom);
                _previousSpeedrunHorizontalPos = leftToRight;
                _timerLeftToRightPosition.SetSliderAmountPercentage(leftToRight);
            }
            else
            {
                if (_timerTopToBottomPosition.gameObject.activeSelf)
                {
                    _timerTopToBottomPosition.gameObject.SetActive(false);
                }
                if (_timerLeftToRightPosition.gameObject.activeSelf)
                {
                    _timerLeftToRightPosition.gameObject.SetActive(false);
                }
            }

            if (displayCarousel)
            {
                if (!_speedrunConfig.gameObject.activeSelf)
                {
                    _speedrunConfig.gameObject.SetActive(true);
                }

                int defaultConfig = _speedrunSettings.GetDefaultTimerConfigIndex();
                int max = _speedrunSettings.GetTimerConfigsCount();
                int config = PlayerPrefs.GetInt(SpeedrunSettings.CONFIG_PLAYER_PREFS_KEY, defaultValue: defaultConfig);

                // protect against bugs or tampering - our menu code is very sensitive to exceptions
                if (config < 0 || config >= max)
                {
                    config = defaultConfig;
                }

                for (int i = 0; i < _speedrunSettings.GetTimerConfigsCount(); i++)
                {
                    SpeedrunSettings.SpeedrunTimerConfig configData = _speedrunSettings.GetTimerConfig(i);

                    LocTerm currentConfigName;
                    if (!string.IsNullOrEmpty(configData.setupNameLocKey))
                    {
                        currentConfigName = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, configData.setupNameLocKey);
                    }
                    else
                    {
                        currentConfigName = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, configData.internalName);
                    }
                    LocExpression currentConfigLoc = new SingleTermLocExpression(currentConfigName);

                    _currentTimerConfigs.Add(currentConfigLoc);
                }

                SpeedrunSettings.SpeedrunTimerConfig currentConfigData = _speedrunSettings.GetTimerConfig(config);

                LocTerm selectedConfigName;
                if (!string.IsNullOrEmpty(currentConfigData.setupNameLocKey))
                {
                    selectedConfigName = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, currentConfigData.setupNameLocKey);
                }
                else
                {
                    selectedConfigName = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, currentConfigData.internalName);
                }
                _speedrunConfig.SetCarouselList(_currentTimerConfigs, new SingleTermLocExpression(selectedConfigName));

                _previousSpeedrunConfig = config;
                _speedrunConfig.SetCarouselSelectionNoNotify(config);
            }
            else
            {
                if (_speedrunConfig.gameObject.activeSelf)
                {
                    _speedrunConfig.gameObject.SetActive(false);
                }
            }
        }
    }
}