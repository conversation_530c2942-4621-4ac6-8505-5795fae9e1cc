// Copyright Isto Inc.

using Isto.Core.Localization;
using Isto.Core.StateMachine;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.UI
{
    /// <summary>
    /// State that manages that controls the flow between the different sub settings states. It also manages the
    /// sub menu tabbed navigation when new categories are selected.
    /// </summary>
    public class UISettingsMenuState : MonoState
    {
        // UNITY HOOKUP

        [Header("UI Component Hookups")]
        [SerializeField] private CanvasGroup _canvasGroup;

        [Header("Confirm Dialogue")]
        [SerializeField] private UISimpleConfirmModalState _confirmationSubState;

        [Header("Reset to Defaults")]
        [SerializeField] private UISimpleConfirmModalState _resetToDefaultsSubState;


        // OTHER FIELDS

        // Used in UISettingsMenuStateEditor
        [HideInInspector] public CanvasGroup _settingsCanvas;
        [HideInInspector] public CanvasGroup _gameplayCanvas;
        [HideInInspector] public CanvasGroup _videoCanvas;
        [HideInInspector] public CanvasGroup _audioCanvas;
        [HideInInspector] public CanvasGroup _controlsCanvas;

        private UISettingsBaseSubState _activeSubMenuState;
        private MonoPushdownStateMachine _menuController;
        private UISettingsBaseSubState _postPopUpMenuState;
        private List<UISettingsBaseSubState> _allSettingsSubMenuStates;
        private List<UISettingsBaseSubState> _settingsCategorySubMenuStates;
        private CoreButton[] _subMenuButtons; // buttons that open each submenu
        private bool _closeSettingsAfterSave;

        // Loc Terms
        private LocTerm _resetCurrentPageMessage;
        private LocTerm _resetAllSettingsMessage;
        private LocTerm _saveSettingsMessage;


        // PROPERTIES

        private bool IsNavigationInTabButtonBar
        {
            get
            {
                bool ret = false;
                for (int i = 0; i < _subMenuButtons.Length; i++)
                {
                    if (_subMenuButtons[i].IsSelected)
                    {
                        ret = true;
                        break;
                    }
                }
                return ret;
            }
        }


        // INJECTION

        protected LocTerm.Factory _localizedStringFactory;

        [Inject]
        public virtual void Inject(LocTerm.Factory localizedFactory)
        {
            _localizedStringFactory = localizedFactory;
        }


        // LIFECYCLE EVENTS

        protected void Awake()
        {
            InitializeSubMenuNavigation();
            InitializeSubMenuButtonsNavigation();

            _resetCurrentPageMessage = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, Constants.UI_SETTINGS_MESSAGE_RESET_CURRENT_PAGE);
            _resetAllSettingsMessage = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, Constants.UI_SETTINGS_MESSAGE_RESET_ALL_SETTINGS);
            _saveSettingsMessage = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, Constants.UI_SETTINGS_MESSAGE_SAVE_SETTINGS);
        }

        public override void Enter(MonoStateMachine controller)
        {
            gameObject.SetActive(true);
            StartCoroutine(UIUtils.FadeInAndEnableCanvasGroup(_canvasGroup, Constants.CANVAS_FADE_TIME));

            SetupStateMachineController(controller);
            _menuController.EnterSubState(_activeSubMenuState);
            UpdateSubMenusTabNavigation();

            RegisterEvents();
        }

        public override void Exit(MonoStateMachine controller)
        {
            StartCoroutine(UIUtils.FadeOutAndDeactivateObject(_canvasGroup, Constants.CANVAS_FADE_TIME, gameObject));
            UnregisterEvents();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            return this;
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            foreach (UISettingsBaseSubState currentSubMenu in _allSettingsSubMenuStates)
            {
                AttachSettingsSubStateHandlers(currentSubMenu);
            }
        }

        private void UnregisterEvents()
        {
            foreach (UISettingsBaseSubState currentSubMenu in _allSettingsSubMenuStates)
            {
                DetachSettingsSubStateHandlers(currentSubMenu);
            }
        }

        private void AttachSettingsSubStateHandlers(UISettingsBaseSubState subState)
        {
            if (subState is not UISettingsSubCategorySubState)
            {
                subState.OnTabDirection += SettingsSubState_OnTabDirection;
            }

            subState.OnSubStateAttemptClose += SettingsSubState_OnRequestClose;
            subState.OnResetToDefaultsSubmit += SettingsSubState_OnResetToDefaults;
            subState.OnResetAllToDefaultsSubmit += SettingsSubState_OnResetAllToDefaults;
            subState.OnSubMenuCategoryChanged += SettingsSubState_OnCategoryChanged;
        }

        private void DetachSettingsSubStateHandlers(UISettingsBaseSubState subState)
        {
            if (subState is not UISettingsSubCategorySubState)
            {
                subState.OnTabDirection -= SettingsSubState_OnTabDirection;
            }

            subState.OnSubStateAttemptClose -= SettingsSubState_OnRequestClose;
            subState.OnResetToDefaultsSubmit -= SettingsSubState_OnResetToDefaults;
            subState.OnResetAllToDefaultsSubmit -= SettingsSubState_OnResetAllToDefaults;
            subState.OnSubMenuCategoryChanged -= SettingsSubState_OnCategoryChanged;
        }

        // This is used by the SettingsTab_Gameplay button that is located in the Unity Editor. It can be found in the
        // c_SettingsTabs container within the UI-Menu_settings prefab.
        public void Button_OpenGameplaySettings()
        {
            RequestChangeSubMenu(SettingsSubMenusEnum.Gameplay);
        }

        // This is used by the SettingsTab_Video button that is located in the Unity Editor. It can be found in the
        // c_SettingsTabs container within the UI-Menu_settings prefab.
        public void Button_OpenVideoSettings()
        {
            RequestChangeSubMenu(SettingsSubMenusEnum.Video);
        }

        // This is used by the SettingsTab_Audio button that is located in the Unity Editor. It can be found in the
        // c_SettingsTabs container within the UI-Menu_settings prefab.
        public void Button_OpenAudioSettings()
        {
            RequestChangeSubMenu(SettingsSubMenusEnum.Audio);
        }

        // This is used by the SettingsTab_Controls button that is located in the Unity Editor. It can be found in the
        // c_SettingsTabs container within the UI-Menu_settings prefab.
        public void Button_OpenControlSettings()
        {
            RequestChangeSubMenu(SettingsSubMenusEnum.Controls);
        }

        // Hooked up from left direction glyph in settings menu prefab
        public void Button_NextTabLeft()
        {
            ChangeTab(UISettingsBaseSubState.TabDirection.Left);
        }

        // Hooked up from right direction glyph in settings menu prefab
        public void Button_NextTabRight()
        {
            ChangeTab(UISettingsBaseSubState.TabDirection.Right);
        }

        private void SettingsSubState_OnTabDirection(UISettingsBaseSubState.TabDirection tabDirection)
        {
            ChangeTab(tabDirection);
        }

        private void SettingsSubState_OnRequestClose(bool isChangesMade)
        {
            if (isChangesMade)
            {
                UISettingsBaseSubState currentState = _menuController.GetCurrentState() as UISettingsBaseSubState;
                if (currentState.RequiresUserValidation)
                {
                    CloseSettingsSubStateWithChangesAndValidation();
                }
                else
                {
                    CloseSettingsSubStateWithChanges();
                }
            }
            else
            {
                _menuController.ExitSubState();
            }
        }

        private void SettingsSubState_OnResetToDefaults()
        {
            _postPopUpMenuState = _activeSubMenuState;

            // callbacks happen after popup substate pops itself
            _resetToDefaultsSubState.SetCallbacks(ConfirmationSubState_ResetToDefaults, ConfirmationSubState_CancelReset);
            
            _resetToDefaultsSubState.SetMainText(_resetCurrentPageMessage);
            _menuController.EnterSubState(_resetToDefaultsSubState);
        }

        private void SettingsSubState_OnResetAllToDefaults()
        {
            _postPopUpMenuState = _activeSubMenuState;

            // callbacks happen after popup substate pops itself
            _resetToDefaultsSubState.SetCallbacks(ConfirmationSubState_ResetAllToDefaults, ConfirmationSubState_CancelReset);
            
            _resetToDefaultsSubState.SetMainText(_resetAllSettingsMessage);
            _menuController.EnterSubState(_resetToDefaultsSubState);
        }

        private void SettingsSubState_OnCategoryChanged()
        {
            UpdateSubMenusTabNavigation();
        }

        private void ConfirmationSubstate_SaveConfirmedIntoValidate()
        {
            if (_menuController.GetCurrentState() is UISettingsBaseSubState currentState)
            {
                currentState.ApplySettingsForUserValidation();
            }

            EnterValidationModalState();
        }

        private void ConfirmationSubState_SaveConfirmed()
        {
            if (_menuController.GetCurrentState() is UISettingsBaseSubState settingsState)
            {
                settingsState.ApplySettingsChanges();
            }

            ClosePopupSubstate();
        }

        private void ConfirmationSubState_SaveCancelled()
        {
            if (_menuController.GetCurrentState() is UISettingsBaseSubState settingsState)
            {
                settingsState.DiscardSettingsChanges();
            }

            ClosePopupSubstate();
        }

        private void ConfirmationSubState_CancelReset()
        {
            ChangeSubMenu(_postPopUpMenuState);
        }

        private void ConfirmationSubState_ResetToDefaults()
        {
            if (_menuController.GetCurrentState() is UISettingsBaseSubState settingsState)
            {
                settingsState.ResetToDefaults();
            }
        }

        private void ConfirmationSubState_ResetAllToDefaults()
        {
            foreach (UISettingsBaseSubState currentSubMenu in _allSettingsSubMenuStates)
            {
                currentSubMenu.ResetToDefaults();
            }
        }


        // ACCESSORS

        private UISettingsBaseSubState GetSubMenuState(SettingsSubMenusEnum menuEnum)
        {
            return _settingsCategorySubMenuStates.Find(state => state.SettingsSubMenu == menuEnum);
        }


        // OTHER METHODS

        private void InitializeSubMenuNavigation()
        {
            _allSettingsSubMenuStates = GetComponentsInChildren<UISettingsBaseSubState>(includeInactive: true).ToList();

#if PLATFORM_GAMECORE
            FilterOutXboxSubMenuStates();
#endif

            // This filters out all the sub categories from the sub menu states. The reason is that the logic for the
            // sub categories is handled in the parent UISettingsBaseSubState.
            _settingsCategorySubMenuStates = _allSettingsSubMenuStates
                .Where(state => !(state is UISettingsSubCategorySubState))
                .ToList();

            _activeSubMenuState = _settingsCategorySubMenuStates.FirstOrDefault();

            if (_activeSubMenuState == null)
            {
                Debug.LogError("No valid UISettingsBaseSubState components found in children. Ensure there are valid submenu states assigned in the scene hierarchy.");
            }
        }

        private void InitializeSubMenuButtonsNavigation()
        {
            _subMenuButtons = _settingsCategorySubMenuStates.Select(state => state.TabButton).ToArray();
        }

        private void SetupStateMachineController(MonoStateMachine controller)
        {
            if (controller is MonoPushdownStateMachine stateMachine)
            {
                _menuController = stateMachine;
            }
            else
            {
                Debug.LogError("Using settings menu without a push down menu controller, this won't work");
            }
        }

        private void ChangeTab(UISettingsBaseSubState.TabDirection tabDirection)
        {
            int currentIndex = _settingsCategorySubMenuStates.IndexOf(_activeSubMenuState);
            UISettingsBaseSubState nextSubState;
            if (tabDirection == UISettingsBaseSubState.TabDirection.Left)
            {
                if (currentIndex == 0)
                {
                    nextSubState = _settingsCategorySubMenuStates[_settingsCategorySubMenuStates.Count - 1];
                }
                else
                {
                    nextSubState = _settingsCategorySubMenuStates[currentIndex - 1];
                }
            }
            else
            {
                nextSubState = _settingsCategorySubMenuStates[(currentIndex + 1) % _settingsCategorySubMenuStates.Count];
            }

            RequestChangeSubMenu(nextSubState.SettingsSubMenu);
        }

        private void RequestChangeSubMenu(SettingsSubMenusEnum menuEnum)
        {
            UISettingsBaseSubState nextSubMenuState = GetSubMenuState(menuEnum);

            if (nextSubMenuState != _menuController.GetCurrentState())
            {
                if (_activeSubMenuState.HasChanges)
                {
                    UISettingsBaseSubState currentState = _menuController.GetCurrentState() as UISettingsBaseSubState;
                    if (currentState.RequiresUserValidation)
                    {
                        ChangeTabWithChangesAndValidation(nextSubMenuState);
                    }
                    else
                    {
                        ChangeTabsWithChanges(nextSubMenuState);
                    }
                }
                else
                {
                    ChangeSubMenu(nextSubMenuState);
                }
            }
        }

        /// <summary>
        /// This flow has the user revert or apply their settings before closing the menu.
        /// </summary>
        private void CloseSettingsSubStateWithChanges()
        {
            EnterConfirmationModalState(targetState: _activeSubMenuState, closeSettingsAfterSave: true);
        }

        /// <summary>
        /// This flow has the user revert or apply their settings, and then view and validate them,
        /// before closing the menu.
        /// </summary>
        private void CloseSettingsSubStateWithChangesAndValidation()
        {
            EnterConfirmationValidationModalState(targetState: _activeSubMenuState, closeSettingsAfterSave: true);
        }

        /// <summary>
        /// This flow has the user revert or apply their settings before changing tabs.
        /// </summary>
        private void ChangeTabsWithChanges(UISettingsBaseSubState menuSubState)
        {
            EnterConfirmationModalState(targetState: menuSubState, closeSettingsAfterSave: false);
        }

        /// <summary>
        /// This flow has the user revert or apply their settings, and then view and validate them,
        /// before changing tabs.
        /// </summary>
        private void ChangeTabWithChangesAndValidation(UISettingsBaseSubState menuSubState)
        {
            EnterConfirmationValidationModalState(targetState: menuSubState, closeSettingsAfterSave: false);
        }

        /// <summary>
        /// Confirmation modal popup will ask user to apply or revert their settings changes.
        /// </summary>
        private void EnterConfirmationModalState(UISettingsBaseSubState targetState, bool closeSettingsAfterSave)
        {
            _closeSettingsAfterSave = closeSettingsAfterSave;
            _postPopUpMenuState = targetState;

            _confirmationSubState.SetCallbacks(ConfirmationSubState_SaveConfirmed, ConfirmationSubState_SaveCancelled);
            _confirmationSubState.SetMainText(_saveSettingsMessage);
            _menuController.EnterSubState(_confirmationSubState);
        }

        /// <summary>
        /// Confirmation modal popup will ask user to apply or revert their settings changes, and if user chooses to
        /// apply, we will then show them a validation popup for keeping or discarding said changes.
        /// </summary>
        private void EnterConfirmationValidationModalState(UISettingsBaseSubState targetState, bool closeSettingsAfterSave)
        {
            _closeSettingsAfterSave = closeSettingsAfterSave;
            _postPopUpMenuState = targetState;

            _confirmationSubState.SetCallbacks(ConfirmationSubstate_SaveConfirmedIntoValidate, ConfirmationSubState_SaveCancelled);
            _confirmationSubState.SetMainText(_saveSettingsMessage);
            _menuController.EnterSubState(_confirmationSubState);
        }

        /// <summary>
        /// Validation modal popup will ask user to confirm or revert their settings choice, but abort the whole thing
        /// after 10 seconds in case the game is not understandable at that point.
        /// </summary>
        private void EnterValidationModalState()
        {
            _confirmationSubState.SetTimer(10);
            _confirmationSubState.SetCallbacks(ConfirmationSubState_SaveConfirmed, ConfirmationSubState_SaveCancelled);
            _confirmationSubState.SetMainText(Loc.GetString(Constants.UI_SETTINGS_MESSAGE_KEEP_SETTINGS));

            _menuController.EnterSubState(_confirmationSubState);
        }

        private void ClosePopupSubstate()
        {
            if (_closeSettingsAfterSave)
            {
                // Closing after popup flow means closing the substate we are currently in as well as this menu
                _menuController.ExitSubState();
                _menuController.ExitSubState();
            }
            else
            {
                // Go to next menu
                ChangeSubMenu(_postPopUpMenuState);
            }
        }

        private void ChangeSubMenu(UISettingsBaseSubState subMenuState)
        {
            if (subMenuState == _activeSubMenuState)
            {
                return;
            }

            _activeSubMenuState = subMenuState;
            ChangeSubMenuState(subMenuState);
            UpdateSubMenusTabNavigation();
        }

        private void ChangeSubMenuState(UISettingsBaseSubState nextSubState)
        {
            nextSubState.KeepNavInStateButtonsBar = IsNavigationInTabButtonBar;
            _menuController.ExitSubState();
            _menuController.EnterSubState(nextSubState);
        }

        private void UpdateSubMenusTabNavigation()
        {
            // Filter out the sub menu button that is currently selected. This way the sub menu tab buttons can skip over it.
            CoreButton[] filteredMenu = _subMenuButtons.Where(subMenuButton => subMenuButton.enabled).ToArray();

            // All the sub menu tab buttons should point to the default selection of the current sub menu.
            foreach (CoreButton subMenuButton in filteredMenu)
            {
                UIUtils.SetNavigation(subMenuButton, UIUtils.NavigationDirection.Down, _activeSubMenuState.FirstSelectable);
            }

            for (int i = 0; i < filteredMenu.Length; i++)
            {
                CoreButton currentButton = filteredMenu[i];
                CoreButton nextButton = filteredMenu[(i + 1) % filteredMenu.Length];
                CoreButton previousButton = filteredMenu[(i - 1 + filteredMenu.Length) % filteredMenu.Length];

                UIUtils.SetNavigation(currentButton, UIUtils.NavigationDirection.Right, nextButton);
                UIUtils.SetNavigation(currentButton, UIUtils.NavigationDirection.Left, previousButton);
            }
        }

#if PLATFORM_GAMECORE
        private void FilterOutXboxSubMenuStates()
        {
            foreach (UISettingsBaseSubState settingsSubMenuState in _allSettingsSubMenuStates)
            {
                ProcessSubMenuState(settingsSubMenuState);
            }
            _allSettingsSubMenuStates.RemoveAll(state => state.RemoveFromXboxBuild);
        }

        private void ProcessSubMenuState(UISettingsBaseSubState settingsSubMenuState)
        {
            if (!settingsSubMenuState.RemoveFromXboxBuild)
                return;

            // If we are to remove this from the Xbox build, then we disable the tab navigation
            // NOTE: We can choose to destroy the navigation objects and pages here if that is preferred
            if (settingsSubMenuState is UISettingsSubCategorySubState subCategoryState)
            {
                subCategoryState.SubTabButton.gameObject.SetActive(false);
            }
            else
            {
                settingsSubMenuState.TabButton.gameObject.SetActive(false);
            }
        }
#endif
    }
}