// Copyright Isto Inc.
using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.Core.UI
{
    /// <summary>
    /// Title menu screen is mostly just menus.
    /// Title menu UI state also can't really be closed, whether it's a navigation thing or just a "press start" thing;
    /// although it could be hidden, it will remain at the bottom of the stack.
    /// In turn this means that all menu states in the title screen will be substates of the main title menu state.
    /// (substates are just states but the flow is a little different since the parent state gets informed when returned to)
    /// In Atrio this meant that the title menu state had the power to manage itself and the state machine didn't
    /// really need any controls, so it remained very simple.
    /// </summary>
    public class SimpleTitleMenuStateMachine : MonoPushdownStateMachine, IUIMenu
    {
        // We could declare a specialized list of menus TitleMenuEnum and deal in those if we wanted this state machine
        // to do some actual work. For now I'll keep it agnostic and simple.
        private MenusEnum _currentMenu;

        public MenusEnum CurrentMenu => _currentMenu;

        public virtual void OpenMenu(MenusEnum menu, OpenMenuArgs args = null)
        {
            _currentMenu = menu;

            if (_currentMenu == MenusEnum.CLOSED)
            {
                CloseMenu();
            }
            else
            {
                // Right now our title menu state machine doesn't really handle anything so it doesn't know its states.
                // This is where we would 
                //EnterSubState(menuState);
                Debug.LogError($"Unhandled");
            }
        }

        public virtual void CloseMenu()
        {
            // If not already in default state, exit out of any substates
            if (_currentState != startState)
            {
                while (_stateStack.Count > 1)
                {
                    ExitSubState();
                }
            }

            _currentMenu = MenusEnum.CLOSED;
        }
    }
}