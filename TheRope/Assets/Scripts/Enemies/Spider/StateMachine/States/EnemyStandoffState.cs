// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyStandoffState : EnemyState
    {
        [SerializeField] private float _holdRadius = 6f;
        [SerializeField] private float _orbitSpeedDegPerSec = 60f;
        [SerializeField] private float _maxWait = 8f;
        private Transform _anchor; // temporary target

        private Transform _shield;
        private float _t, _angle;


        public override void Enter(MonoStateMachine m)
        {
            base.Enter(m);
            _t = 0f;
            _angle = 0f;

            if (_anchor == null)
            {
                _anchor = new GameObject("StandoffAnchor").transform;
                _anchor.SetParent(_spiderController.transform.parent, false);
            }

            _anchor.position = RingPoint();
            _spiderController.SetTarget(_anchor);
            _debugStateMessage = "Spider is in Standoff (waiting at shield)";
        }

        public override MonoState Run(MonoStateMachine m)
        {
            _t += Time.deltaTime;

            // Leave if shield no longer protects the player (your condition here)
            if (PlayerExposedOrShieldDown())
                return _spiderController.GetState(SpiderController.EnemyStateEnum.Chase);

            // Timeout fallback
            if (_t >= _maxWait)
                return _spiderController.GetState(SpiderController.EnemyStateEnum.Patrol);

            // Orbit around shield
            _angle += _orbitSpeedDegPerSec * Time.deltaTime;
            _anchor.position = RingPoint();

            return this;
        }

        public override void Exit(MonoStateMachine m)
        {
            /* keep anchor for reuse */
        }

        private Vector3 RingPoint()
        {
            if (!_shield) return _spiderController.transform.position;
            var center = _shield.position;
            var rot = Quaternion.Euler(0f, _angle, 0f);
            return center + rot * (Vector3.forward * _holdRadius);
        }

        private bool PlayerExposedOrShieldDown()
        {
            // Replace with your actual condition:
            // e.g., !shield.activeInHierarchy || !playerInsideShield
            return _spiderController.CurrentTarget &&
                   _spiderController.GetDistanceToCurrentTarget() <= _spiderController.AttackDistance;
        }
    }
}