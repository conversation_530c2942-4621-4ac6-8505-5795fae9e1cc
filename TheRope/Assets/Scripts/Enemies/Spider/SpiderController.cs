// Copyright Isto Inc.

using Isto.Core.Beings;
using System;
using System.Collections.Generic;
using Isto.Core.StateMachine;
using RootMotion.Demos;
using UnityEngine;

namespace Isto.TRP.Enemies
{

    public class SpiderController : PUN2EnemyController
    {
        public enum EnemyStateEnum
        {
            Idle,
            Patrol,
            Chase,
            Attack,
            Standoff,
            Retreat,
            ScarePlayer,
            Dead
        }


        // UNITY HOOKUP

        [Header("Component Hookups")]
        [SerializeField] private MechSpider _mechSpider;
        [SerializeField] private CapsuleCollider _capsuleCollider;

        [Header("Enemy Config")]
        [SerializeField] private string _playerTag = "Player";
        [SerializeField] private float _speed = 6f;
        [SerializeField] private float _turnSpeed = 60f;
        [SerializeField] private float _detectionRadius = 60f;
        [SerializeField] private float _loseSightDistance = 85f;
        [SerializeField] private float _attackDistance = 15f;
        [SerializeField] private float _boredDuration = 10f;
        [SerializeField] private float _idleDuration = 5f;
        [SerializeField] private float _patrolDuration = 45f;

        [Header("Enemy MonoStates")]
        [SerializeField] private EnemyIdleState _idleState;
        [SerializeField] private EnemyPatrolState _patrolState;
        [SerializeField] private EnemyChaseState _chaseState;
        [SerializeField] private EnemyAttackState _attackState;
        [SerializeField] private EnemyStandoffState _standoffState;
        [SerializeField] private EnemyRetreatState _retreatState;
        [SerializeField] private EnemyScarePlayerState _scarePlayerState;
        [SerializeField] private EnemyDeadState _deadState;


        // OTHER FIELDS

        private Dictionary<EnemyStateEnum, MonoState> _stateMap = new Dictionary<EnemyStateEnum, MonoState>();
        private Transform _patrolPointContainer;
        private List<Transform> _patrolPoints;
        private Transform _playerTarget;
        private Transform _localPlayerCamera;
        private bool _isAttacking;


        // PROPERTIES

        public float LoseSightDistance => _loseSightDistance;
        public float AttackDistance => _attackDistance;
        public float BoredDuration => _boredDuration;
        public float IdleDuration => _idleDuration;
        public float PatrolDuration => _patrolDuration;
        public bool IsAttacking => _isAttacking;
        public List<Transform> PatrolPoints => _patrolPoints;


        // EVENTS

        public event Action<EnemyStateEnum, Transform> OnHostEnemyStateChanged;
        public event Action<Transform> OnHostEnemyTargetChanged;


        // LIFECYCLE EVENTS

        protected void Awake()
        {
            SetupStateMap();
            InitializePatrolPoints();
        }

        protected override void Start()
        {
            base.Start();

            // Host (or single player) owns initial state.
            if (!IsNetworked || IsHost)
            {
                SetState(EnemyStateEnum.Idle);
            }
        }

        protected override void Update()
        {
            base.Update();



            // If we are attacking, don't move.
            if (CurrentTarget != null && !_isAttacking)
            {
                MoveTowards(CurrentTarget.position);
            }
        }

        // ACCESSORS

        public MonoState GetState(EnemyStateEnum newState)
        {
            return _stateMap[newState];
        }

        public void SetTargetToPlayer()
        {
            if (_playerTarget != null)
            {
                SetTarget(_playerTarget);
            }
        }

        public override void SetTarget(Transform newTarget)
        {
            base.SetTarget(newTarget);

            // Only host emits the change; remote peers mirror via ApplyRemoteTarget.
            if (!IsNetworked || IsHost)
            {
                OnHostEnemyTargetChanged?.Invoke(newTarget);
            }
        }

        public string GetDebugStateMessage()
        {
            string debugStateMessage = "No State";
            EnemyState state = _currentState as EnemyState;
            if (state != null)
            {
                debugStateMessage = state.DebugStateMessage;
            }
            return debugStateMessage;
        }

        public float GetDistanceToCurrentTarget()
        {
            float distance = 0f;
            if (CurrentTarget != null)
            {
                distance = Vector3.Distance(transform.position, CurrentTarget.position);
            }
            return distance;
        }

        public override void ChangeState(MonoState nextState)
        {
            base.ChangeState(nextState);

            // Only host emits the change; remote peers mirror via ApplyRemoteState.
            if (IsNetworked && !IsHost)
                return;

            EnemyStateEnum stateEnumState = EnemyStateEnum.Idle;
            foreach (KeyValuePair<EnemyStateEnum, MonoState> kv in _stateMap)
            {
                if (kv.Value == nextState)
                {
                    stateEnumState = kv.Key;
                    break;
                }
            }

            OnHostEnemyStateChanged?.Invoke(stateEnumState, CurrentTarget);
        }

        public void SetIsAttacking(bool isAttacking)
        {
            _isAttacking = isAttacking;
        }

        private void SetState(EnemyStateEnum newState)
        {
            if (IsNetworked && !IsHost)
                return;

            base.ChangeState(_stateMap[newState]);
        }


        // OTHER METHODS

        public bool TryFindNearestPlayerWithinRadius()
        {
            Collider[] hits = Physics.OverlapSphere(
                transform.position,
                _detectionRadius,
                ~0,
                QueryTriggerInteraction.Ignore);

            Transform nearestPlayerTransform = null;
            float detectionRadius = _detectionRadius * _detectionRadius;

            foreach (Collider hitCollider in hits)
            {
                if (!hitCollider.CompareTag(_playerTag))
                    continue;

                float magnitude = (hitCollider.transform.position - transform.position).sqrMagnitude;
                PlayerHealth playerHealth = hitCollider.GetComponent<PlayerHealth>();
                if (magnitude <= detectionRadius && playerHealth.Alive)
                {
                    detectionRadius = magnitude;
                    nearestPlayerTransform = hitCollider.transform;
                }
            }

            _playerTarget = nearestPlayerTransform;
            return nearestPlayerTransform != null;
        }

        public void FindNearestPlayer()
        {
            // Not great but is good enough for now
            GameObject[] players = GameObject.FindGameObjectsWithTag(_playerTag);

            Transform nearestPlayerTransform = null;
            float bestDistance = float.MaxValue;

            foreach (GameObject currentPlayer in players)
            {
                float magnitude = (currentPlayer.transform.position - transform.position).sqrMagnitude;
                PlayerHealth playerHealth = currentPlayer.GetComponent<PlayerHealth>();
                if (magnitude < bestDistance && playerHealth.Alive)
                {
                    bestDistance = magnitude;
                    nearestPlayerTransform = currentPlayer.transform;
                }
            }

            _playerTarget = nearestPlayerTransform;
            SetTarget(nearestPlayerTransform);
        }

        public int GetRandomPatrolPointIndex(int excludeIndex = -1)
        {
            int randomIndex;
            if (_patrolPoints == null || _patrolPoints.Count == 0)
            {
                Debug.LogError("No patrol points found!");
                randomIndex = -1;
            }
            else if (_patrolPoints.Count == 1)
            {
                Debug.LogWarning("Only one patrol point found!");
                randomIndex = 0;
            }
            else if (excludeIndex >= 0 && excludeIndex < _patrolPoints.Count)
            {
                int range = UnityEngine.Random.Range(0, _patrolPoints.Count - 1);
                if (range >= excludeIndex)
                {
                    range++;
                }
                randomIndex = range;
            }
            else
            {
                randomIndex = UnityEngine.Random.Range(0, _patrolPoints.Count);
            }

            return randomIndex;
        }

        public void SetPatrolTargetByIndex(int patrolPointIndex)
        {
            Transform patrolPoint;
            if (_patrolPoints != null && patrolPointIndex >= 0 && patrolPointIndex < _patrolPoints.Count)
            {
                patrolPoint = _patrolPoints[patrolPointIndex];
            }
            else
            {
                patrolPoint = null;
            }

            SetTarget(patrolPoint);
        }

        public Transform GetPatrolTransformByIndex(int index)
        {
            Transform patrolPoint;
            if (index < 0 || index >= _patrolPoints.Count)
            {
                patrolPoint = null;
            }
            else
            {
                patrolPoint = _patrolPoints[index];
            }

            return patrolPoint;
        }

        public void ApplyRemoteState(EnemyStateEnum state, Transform resolvedTarget)
        {
            base.SetTarget(resolvedTarget);
            base.ChangeState(_stateMap[state]);
        }

        public void ApplyRemoteTarget(Transform resolvedTarget)
        {
            base.SetTarget(resolvedTarget);
        }

        public int PatrolIndexOf(Transform patrolPointTransform)
        {
            int index = -1;
            if (_patrolPoints != null && patrolPointTransform != null)
            {
                index = _patrolPoints.IndexOf(patrolPointTransform);
            }

            return index;
        }

        private void SetupStateMap()
        {
            _stateMap[EnemyStateEnum.Idle] = _idleState;
            _stateMap[EnemyStateEnum.Patrol] = _patrolState;
            _stateMap[EnemyStateEnum.Chase] = _chaseState;
            _stateMap[EnemyStateEnum.Attack] = _attackState;
            _stateMap[EnemyStateEnum.Standoff] = _standoffState;
            _stateMap[EnemyStateEnum.Retreat] = _retreatState;
            _stateMap[EnemyStateEnum.ScarePlayer] = _scarePlayerState;
            _stateMap[EnemyStateEnum.Dead] = _deadState;
        }

        // This movement is adapted from the MechSpiderController.cs example from RootMotion.
        private void MoveTowards(Vector3 targetPos)
        {
            Vector3 dir = (targetPos - transform.position);
            dir.y = 0f;

            if (dir.sqrMagnitude < 0.0001f)
                return;

            Quaternion look = Quaternion.LookRotation(dir.normalized);
            transform.rotation = Quaternion.RotateTowards(transform.rotation, look, _turnSpeed * Time.deltaTime);

            transform.Translate(
                Vector3.forward * _speed * Time.deltaTime * _mechSpider.scale,
                Space.Self);
        }

        // Temporary patrol point discovery via a scene game object. Replace it with a proper locator when we
        // refactor or choose a different system for patrol points/enemy AI.
        private void InitializePatrolPoints()
        {
            GameObject go = GameObject.Find("c_PatrolPoints");
            _patrolPointContainer = go ? go.transform : null;

            _patrolPoints = new List<Transform>();
            if (_patrolPointContainer != null)
            {
                foreach (Transform child in _patrolPointContainer)
                {
                    _patrolPoints.Add(child);
                }
            }
        }

        private void OnTriggerEnter(Collider other)
        {

        }

        private void OnTriggerStay(Collider other)
        {

        }

        private void OnTriggerExit(Collider other)
        {
        }
    }
}